# 🚀 LINE 預約系統部署指南

本指南將協助您將 LINE 預約系統從開發環境部署到生產環境。

## 📋 部署概覽

### 部署架構
```
用戶 → Vercel/Netlify (前端) → Supabase (後端/資料庫) → LINE API
```

### 部署階段
1. **本地環境完整配置** (30分鐘)
2. **Supabase 生產環境設置** (20分鐘)
3. **LINE Channel 配置** (15分鐘)
4. **前端部署** (15分鐘)
5. **測試和驗證** (20分鐘)

**總預估時間**: 約 100 分鐘

## 🎯 階段 1: 本地環境完整配置

### 1.1 檢查系統需求

運行配置檢查腳本：
```bash
npm run check-config
```

### 1.2 創建 Supabase 專案

1. 前往 [supabase.com](https://supabase.com)
2. 點擊 "New Project"
3. 填寫專案資訊：
   - **Name**: `line-booking-production`
   - **Database Password**: 使用強密碼
   - **Region**: `Asia Pacific (Singapore)`

### 1.3 設置資料庫

1. 前往 Supabase Dashboard → SQL Editor
2. 執行初始化腳本：
   ```sql
   -- 複製 database/init.sql 的內容並執行
   ```
3. 執行 RLS 政策腳本：
   ```sql
   -- 複製 database/rls-policies.sql 的內容並執行
   ```

### 1.4 配置環境變數

複製並編輯環境變數：
```bash
cp .env.example .env.local
```

填入實際值：
```env
# Supabase 配置
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# LINE Login 配置 (稍後填入)
VITE_LINE_CHANNEL_ID=
VITE_LINE_CHANNEL_SECRET=

# 應用配置
VITE_APP_URL=http://localhost:5173
```

## 🔐 階段 2: LINE Channel 配置

### 2.1 創建 LINE Login Channel

1. 前往 [LINE Developers Console](https://developers.line.biz/console/)
2. 創建 Provider 和 Channel
3. 記錄 Channel ID 和 Secret

### 2.2 設置 Callback URL

在 LINE Channel 設定中添加：
```
開發環境: http://localhost:5173/auth/callback
生產環境: https://your-domain.com/auth/callback
```

### 2.3 更新環境變數

```env
VITE_LINE_CHANNEL_ID=your_line_channel_id
VITE_LINE_CHANNEL_SECRET=your_line_channel_secret
```

## 🌐 階段 3: 生產環境部署

### 3.1 選擇部署平台

推薦選項：
- **Vercel** (推薦) - 與 Supabase 整合良好
- **Netlify** - 簡單易用
- **自託管** - 完全控制

### 3.2 Vercel 部署 (推薦)

1. 安裝 Vercel CLI：
   ```bash
   npm i -g vercel
   ```

2. 登入 Vercel：
   ```bash
   vercel login
   ```

3. 部署專案：
   ```bash
   vercel --prod
   ```

4. 設置環境變數：
   ```bash
   vercel env add VITE_SUPABASE_URL
   vercel env add VITE_SUPABASE_ANON_KEY
   vercel env add VITE_LINE_CHANNEL_ID
   vercel env add VITE_LINE_CHANNEL_SECRET
   ```

### 3.3 Netlify 部署

1. 前往 [netlify.com](https://netlify.com)
2. 連接 GitHub 倉庫
3. 設置建構命令：
   ```
   Build command: npm run build
   Publish directory: dist
   ```
4. 在 Environment variables 中添加環境變數

### 3.4 更新 Callback URLs

部署完成後，更新以下設定：

**LINE Channel**:
```
https://your-domain.com/auth/callback
```

**Supabase Auth**:
```
Site URL: https://your-domain.com
Redirect URLs: https://your-domain.com/auth/callback
```

## ✅ 階段 4: 測試和驗證

### 4.1 功能測試清單

運行自動化測試：
```bash
npm run test:deployment
```

手動測試：
- [ ] 首頁載入正常
- [ ] LINE 登入功能
- [ ] 預約流程完整
- [ ] 會員中心功能
- [ ] 票券系統
- [ ] 響應式設計

### 4.2 效能檢查

1. **Lighthouse 分析**:
   ```bash
   npm run lighthouse
   ```

2. **載入速度測試**:
   - 首頁載入 < 3秒
   - 頁面切換 < 1秒

### 4.3 安全性檢查

- [ ] HTTPS 強制啟用
- [ ] 環境變數未暴露
- [ ] CORS 設置正確
- [ ] RLS 政策生效

## 🔧 故障排除

### 常見問題

#### 1. 部署失敗
```bash
# 檢查建構日誌
npm run build

# 檢查依賴
npm audit
```

#### 2. 環境變數問題
```bash
# 驗證環境變數
npm run check-env
```

#### 3. 資料庫連接失敗
- 檢查 Supabase URL 和 Key
- 確認 RLS 政策設置
- 檢查網路連接

#### 4. LINE Login 失敗
- 驗證 Channel ID 和 Secret
- 檢查 Callback URL 設置
- 確認 Scope 配置

## 📊 監控和維護

### 設置監控

1. **Supabase 監控**:
   - 資料庫效能
   - API 使用量
   - 錯誤日誌

2. **前端監控**:
   - 頁面載入時間
   - 錯誤追蹤
   - 用戶行為分析

### 定期維護

- **每週**: 檢查錯誤日誌
- **每月**: 更新依賴套件
- **每季**: 安全性審查
- **每年**: 效能優化

## 🆘 支援資源

- [Supabase 文檔](https://supabase.com/docs)
- [LINE Login 文檔](https://developers.line.biz/en/docs/line-login/)
- [Vercel 文檔](https://vercel.com/docs)
- [專案 GitHub Issues](https://github.com/your-repo/issues)

---

**下一步**: 執行 `npm run deploy-check` 開始部署流程
