# LINE Login 設置指南

本指南將協助您設置 LINE Login 功能，讓用戶可以使用 LINE 帳號登入預約系統。

## 📋 前置準備

1. **LINE Developers 帳號**: 前往 [developers.line.biz](https://developers.line.biz) 註冊
2. **Supabase 專案**: 確保已完成 Supabase 設置（參考 `SUPABASE_SETUP.md`）
3. **網域名稱**: 生產環境需要 HTTPS 網域

## 🚀 步驟 1: 創建 LINE Login Channel

### 1.1 登入 LINE Developers Console

1. 前往 [LINE Developers Console](https://developers.line.biz/console/)
2. 使用您的 LINE 帳號登入
3. 如果是第一次使用，需要同意開發者條款

### 1.2 創建 Provider

1. 點擊 "Create a new provider"
2. 填寫 Provider 資訊：
   - **Provider name**: `線上預約系統` (或您的公司名稱)
   - **Description**: `提供線上預約服務的平台`
3. 點擊 "Create"

### 1.3 創建 LINE Login Channel

1. 在 Provider 頁面中，點擊 "Create a new channel"
2. 選擇 "LINE Login"
3. 填寫 Channel 資訊：
   - **Channel name**: `LINE 預約系統`
   - **Channel description**: `線上預約服務系統，支援 LINE 快速登入`
   - **App type**: `Web app`
   - **Email address**: 您的聯絡信箱
4. 同意條款並點擊 "Create"

## 🔧 步驟 2: 配置 LINE Login 設定

### 2.1 基本設定

1. 進入剛創建的 Channel
2. 前往 **Basic settings** 標籤
3. 記錄以下資訊（稍後會用到）：
   - **Channel ID**
   - **Channel secret**

### 2.2 LINE Login 設定

1. 前往 **LINE Login** 標籤
2. 設置 **Callback URL**：
   
   **開發環境**:
   ```
   http://localhost:5173/auth/callback
   ```
   
   **生產環境**:
   ```
   https://yourdomain.com/auth/callback
   ```

3. 設置 **Scope**:
   - ✅ `profile` (必須)
   - ✅ `openid` (必須)
   - ✅ `email` (可選，建議啟用)

4. **Bot link feature**: 可以保持關閉

### 2.3 進階設定（可選）

1. **ID token signature algorithm**: 保持預設 `HS256`
2. **Consent screen**: 可以自定義同意畫面的文字和圖片

## 🔐 步驟 3: 配置 Supabase Auth

### 3.1 啟用 LINE Provider

1. 前往 Supabase Dashboard
2. 進入您的專案
3. 前往 **Authentication** > **Providers**
4. 找到 "Line" 並點擊啟用

### 3.2 設置 LINE Provider 資訊

1. 在 LINE Provider 設定中填入：
   - **Client ID**: LINE Channel ID
   - **Client Secret**: LINE Channel Secret
2. 點擊 "Save"

### 3.3 設置 Redirect URLs

1. 前往 **Authentication** > **URL Configuration**
2. 設置以下 URL：
   
   **Site URL**:
   ```
   http://localhost:5173  (開發環境)
   https://yourdomain.com  (生產環境)
   ```
   
   **Redirect URLs**:
   ```
   http://localhost:5173/auth/callback
   https://yourdomain.com/auth/callback
   ```

## 🌐 步驟 4: 配置環境變數

### 4.1 更新 .env 文件

在專案根目錄的 `.env` 文件中添加：

```env
# LINE Login 配置
VITE_LINE_CHANNEL_ID=your_line_channel_id_here
VITE_LINE_CHANNEL_SECRET=your_line_channel_secret_here

# Supabase 配置（如果還沒有的話）
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# 應用配置
VITE_APP_URL=http://localhost:5173
```

### 4.2 環境變數說明

- `VITE_LINE_CHANNEL_ID`: LINE Channel 的 Channel ID
- `VITE_LINE_CHANNEL_SECRET`: LINE Channel 的 Channel Secret
- `VITE_SUPABASE_URL`: Supabase 專案 URL
- `VITE_SUPABASE_ANON_KEY`: Supabase 匿名金鑰

## ✅ 步驟 5: 測試 LINE Login

### 5.1 重啟開發服務器

```bash
npm run dev
```

### 5.2 測試登入流程

1. 打開瀏覽器前往 `http://localhost:5173/login`
2. 點擊 "使用 LINE 登入" 按鈕
3. 應該會重定向到 LINE 登入頁面
4. 使用您的 LINE 帳號登入
5. 授權應用存取您的資料
6. 應該會重定向回應用並完成登入

### 5.3 檢查登入狀態

登入成功後：
1. 檢查瀏覽器開發者工具的 Console 是否有錯誤
2. 前往會員中心查看用戶資料是否正確顯示
3. 檢查 Supabase Dashboard 的 Authentication 頁面是否有新用戶

## 🔍 故障排除

### 常見問題

#### 1. "LINE Login 尚未配置" 錯誤
- **原因**: 環境變數未正確設置
- **解決**: 檢查 `.env` 文件中的 `VITE_LINE_CHANNEL_ID` 是否正確

#### 2. 重定向錯誤
- **原因**: Callback URL 設置不正確
- **解決**: 確保 LINE Channel 和 Supabase 的 Callback URL 一致

#### 3. 授權失敗
- **原因**: Channel Secret 錯誤或 Scope 設置問題
- **解決**: 重新檢查 Channel Secret 和 Scope 設定

#### 4. 用戶資料未保存
- **原因**: 資料庫權限或 RLS 政策問題
- **解決**: 檢查 Supabase 的 RLS 政策是否正確設置

### 除錯步驟

1. **檢查環境變數**:
   ```bash
   echo $VITE_LINE_CHANNEL_ID
   ```

2. **檢查 Console 錯誤**:
   - 打開瀏覽器開發者工具
   - 查看 Console 和 Network 標籤的錯誤訊息

3. **檢查 Supabase 日誌**:
   - 前往 Supabase Dashboard
   - 查看 Logs 頁面的錯誤訊息

## 🚀 生產環境部署

### 部署前檢查清單

- [ ] 已設置生產環境的網域名稱
- [ ] 已在 LINE Channel 中添加生產環境的 Callback URL
- [ ] 已在 Supabase 中設置生產環境的 Redirect URLs
- [ ] 已更新生產環境的環境變數
- [ ] 已測試完整的登入流程

### 安全性考量

1. **HTTPS 必須**: 生產環境必須使用 HTTPS
2. **環境變數保護**: 不要將 Channel Secret 暴露在客戶端
3. **CORS 設置**: 確保 Supabase 的 CORS 設置正確
4. **定期更新**: 定期更新 Channel Secret

## 📚 進階功能

### 自動註冊獎勵

系統會自動為新註冊用戶創建歡迎票券：
- 首次預約 9 折優惠券（30天有效）
- NT$100 回饋金（90天有效）

### 用戶資料同步

- 自動同步 LINE 用戶的顯示名稱和頭像
- 支援用戶資料更新
- 保護用戶隱私，僅存儲必要資訊

## 🆘 需要幫助？

如果遇到問題，請檢查：

1. [LINE Login 官方文檔](https://developers.line.biz/en/docs/line-login/)
2. [Supabase Auth 文檔](https://supabase.com/docs/guides/auth)
3. 專案的 `spec.md` 文件了解系統架構

---

**重要提醒**: 
- 請妥善保管您的 Channel Secret，不要提交到版本控制系統
- 定期檢查和更新安全設置
- 遵守 LINE 的使用條款和隱私政策
