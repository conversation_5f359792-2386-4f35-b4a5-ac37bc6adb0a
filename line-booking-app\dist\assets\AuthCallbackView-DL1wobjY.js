import{d as x,u as b,r as c,h,j as g,e as k,c as n,a as s,t as y,b as _,f as q,o as i}from"./index-8Cki6B3T.js";const E={class:"min-h-screen bg-gray-50 flex items-center justify-center p-4"},S={class:"card max-w-md w-full text-center"},C={key:0,class:"py-8"},T={class:"text-gray-600"},z={key:1,class:"py-8"},A={class:"text-gray-600 mb-6"},B={key:2,class:"py-8"},P=x({__name:"AuthCallbackView",setup(L){const l=k(),r=g(),o=b(),d=c(!0),u=c(null),a=c("驗證登入資訊...");h(async()=>{try{if(o.clearError(),r.query.error)throw new Error(r.query.error_description||"登入失敗");r.query.code&&r.query.state?await f():r.hash.includes("access_token")||r.query.access_token?await p():await v()}catch(t){u.value=t instanceof Error?t.message:"未知錯誤",console.error("認證回調處理失敗:",t)}finally{d.value=!1}});const f=async()=>{a.value="處理 LINE 登入...";const t=r.query.code,e=r.query.state;if(!t||!e)throw new Error("缺少必要的認證參數");await o.handleLineCallback(t,e),a.value="登入成功，正在跳轉...",await new Promise(w=>setTimeout(w,1e3));const m=r.query.redirect||"/";l.replace(m)},p=async()=>{if(a.value="處理 Supabase 認證...",await new Promise(t=>setTimeout(t,2e3)),o.isAuthenticated){a.value="登入成功，正在跳轉...",await new Promise(e=>setTimeout(e,1e3));const t=r.query.redirect||"/";l.replace(t)}else throw new Error("Supabase 認證失敗")},v=async()=>{if(a.value="檢查登入狀態...",await o.initAuth(),o.isAuthenticated){a.value="已登入，正在跳轉...",await new Promise(e=>setTimeout(e,1e3));const t=r.query.redirect||"/";l.replace(t)}else throw new Error("未找到有效的登入會話")};return(t,e)=>(i(),n("div",E,[s("div",S,[d.value?(i(),n("div",C,[e[1]||(e[1]=s("div",{class:"w-16 h-16 border-4 border-line-green border-t-transparent rounded-full animate-spin mx-auto mb-4"},null,-1)),e[2]||(e[2]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-2"},"處理登入中...",-1)),s("p",T,y(a.value),1),e[3]||(e[3]=s("div",{class:"mt-6 max-w-xs mx-auto"},[s("div",{class:"bg-gray-200 rounded-full h-2"},[s("div",{class:"bg-line-green h-2 rounded-full animate-pulse",style:{width:"60%"}})])],-1))])):u.value?(i(),n("div",z,[e[4]||(e[4]=s("div",{class:"w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center"},[s("svg",{class:"w-8 h-8 text-red-600",fill:"currentColor",viewBox:"0 0 20 20"},[s("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])],-1)),e[5]||(e[5]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-2"},"登入失敗",-1)),s("p",A,y(u.value),1),s("button",{onClick:e[0]||(e[0]=m=>_(l).push("/login")),class:"btn-primary"}," 重新登入 ")])):(i(),n("div",B,e[6]||(e[6]=[q('<div class="w-16 h-16 bg-green-100 rounded-full mx-auto mb-4 flex items-center justify-center"><svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg></div><h2 class="text-xl font-semibold text-gray-900 mb-2">登入成功</h2><p class="text-gray-600 mb-6">正在跳轉到首頁...</p>',3)])))])]))}});export{P as default};
