import{d as k,u as S,r as p,g as T,h as z,c as i,a as e,i as g,t as r,b as l,F as B,k as C,q as j,v as D,e as F,p as x,o as a}from"./index-CK6tErNr.js";import{u as $}from"./booking-CCzFsPCM.js";const L={class:"card mb-6"},N={class:"space-y-4"},V={class:"flex items-center space-x-3"},M={class:"font-medium text-gray-900"},A={class:"text-sm text-gray-600"},H={class:"flex items-center space-x-3"},q={class:"font-medium text-gray-900"},I={class:"text-sm text-gray-600"},P={class:"flex items-center space-x-3"},U={class:"font-medium text-gray-900"},W={key:0,class:"card mb-6"},E={class:"space-y-3"},R=["onClick"],G={key:0,class:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20"},J={class:"flex-1"},K={class:"font-medium text-gray-900"},O={class:"text-sm text-gray-600"},Q={class:"flex items-center justify-between mt-1"},X={class:"text-sm text-line-green font-medium"},Y={key:0,class:"text-xs text-gray-500"},Z={class:"card mb-6"},ee={class:"space-y-3"},te={class:"flex justify-between"},se={class:"font-medium"},oe={key:0,class:"flex justify-between text-green-600"},ne={class:"border-t border-gray-200 pt-3 flex justify-between text-lg font-bold"},re={class:"text-line-green"},le={class:"card mb-6"},ie={class:"card"},ae=["disabled"],de={key:0,class:"flex items-center justify-center space-x-2"},ue={key:1},pe=k({__name:"BookingConfirmView",setup(ce){const f=F(),s=$(),u=S(),c=p(""),m=p(!1),v=T(()=>!s.bookingForm.booking_date||!s.bookingForm.booking_time?"":`${new Date(s.bookingForm.booking_date).toLocaleDateString("zh-TW",{year:"numeric",month:"long",day:"numeric",weekday:"long"})} ${s.bookingForm.booking_time}`),d=o=>s.bookingForm.selected_tickets.includes(o),b=o=>{s.toggleTicket(o)},y=o=>o.discount_type==="percentage"?`${o.discount_value}% 折扣`:o.discount_type==="fixed_amount"?`NT$ ${o.discount_value} 折抵`:"優惠券",_=o=>new Date(o).toLocaleDateString("zh-TW"),h=()=>{s.setNotes(c.value)},w=async()=>{if(!u.user){f.push("/login");return}m.value=!0;try{const o=await s.createBooking(u.user.id);f.push({name:"booking-success",query:{bookingId:o?.id}})}catch(o){console.error("預約失敗:",o)}finally{m.value=!1}};return z(async()=>{u.user&&s.userTickets.length===0&&await s.fetchUserTickets(u.user.id),s.bookingForm.notes&&(c.value=s.bookingForm.notes)}),(o,t)=>(a(),i("div",null,[t[15]||(t[15]=e("div",{class:"mb-6"},[e("h2",{class:"text-xl font-bold text-gray-900 mb-2"},"確認預約"),e("p",{class:"text-gray-600"},"請確認預約資訊無誤後提交")],-1)),e("div",L,[t[5]||(t[5]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"預約資訊",-1)),e("div",N,[e("div",V,[t[1]||(t[1]=e("div",{class:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("p",M,r(l(s).selectedStaff?.name),1),e("p",A,r(l(s).selectedStaff?.title),1)])]),e("div",H,[t[2]||(t[2]=e("div",{class:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("p",q,r(l(s).selectedService?.name),1),e("p",I,r(l(s).selectedService?.duration)+" 分鐘",1)])]),e("div",P,[t[4]||(t[4]=e("div",{class:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("p",U,r(v.value),1),t[3]||(t[3]=e("p",{class:"text-sm text-gray-600"},"請準時到達",-1))])])])]),l(s).userTickets.length>0?(a(),i("div",W,[t[7]||(t[7]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"使用票券",-1)),e("div",E,[(a(!0),i(B,null,C(l(s).userTickets,n=>(a(),i("div",{key:n.id,onClick:me=>b(n.id),class:x(["flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors duration-200",{"border-line-green bg-green-50":d(n.id),"border-gray-200 hover:border-gray-300":!d(n.id)}])},[e("div",{class:x(["w-5 h-5 rounded border-2 flex items-center justify-center",{"border-line-green bg-line-green":d(n.id),"border-gray-300":!d(n.id)}])},[d(n.id)?(a(),i("svg",G,t[6]||(t[6]=[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"},null,-1)]))):g("",!0)],2),e("div",J,[e("p",K,r(n.title),1),e("p",O,r(n.description),1),e("div",Q,[e("span",X,r(y(n)),1),n.expiry_date?(a(),i("span",Y," 到期："+r(_(n.expiry_date)),1)):g("",!0)])])],10,R))),128))])])):g("",!0),e("div",Z,[t[11]||(t[11]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"費用明細",-1)),e("div",ee,[e("div",te,[t[8]||(t[8]=e("span",{class:"text-gray-600"},"服務費用",-1)),e("span",se,"NT$ "+r(l(s).totalPrice.toLocaleString()),1)]),l(s).discountAmount>0?(a(),i("div",oe,[t[9]||(t[9]=e("span",null,"票券折扣",-1)),e("span",null,"-NT$ "+r(l(s).discountAmount.toLocaleString()),1)])):g("",!0),e("div",ne,[t[10]||(t[10]=e("span",null,"總計",-1)),e("span",re,"NT$ "+r(l(s).finalPrice.toLocaleString()),1)])])]),e("div",le,[t[12]||(t[12]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"備註",-1)),j(e("textarea",{"onUpdate:modelValue":t[0]||(t[0]=n=>c.value=n),onInput:h,placeholder:"請輸入特殊需求或備註事項...",rows:"3",class:"input-field resize-none"},null,544),[[D,c.value]])]),e("div",ie,[e("button",{onClick:w,disabled:m.value,class:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"},[m.value?(a(),i("div",de,t[13]||(t[13]=[e("div",{class:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"},null,-1),e("span",null,"提交中...",-1)]))):(a(),i("span",ue,"確認預約"))],8,ae),t[14]||(t[14]=e("p",{class:"text-xs text-gray-500 text-center mt-3"}," 點擊確認即表示您同意我們的服務條款和取消政策 ",-1))])]))}});export{pe as default};
