import{d as F,g as l,j,c as s,a as e,t as u,b as S,e as N,F as v,k as x,m as z,n as L,i as g,p as m,o as n}from"./index-CK6tErNr.js";import{u as V}from"./booking-CCzFsPCM.js";const M={class:"min-h-screen bg-gray-50"},R={class:"bg-white shadow-sm sticky top-0 z-10"},T={class:"max-w-md mx-auto px-4 py-4 flex items-center justify-between"},D={class:"text-lg font-semibold text-gray-900"},E={class:"bg-white border-b border-gray-200"},P={class:"max-w-md mx-auto px-4 py-4"},$={class:"flex items-center justify-between"},q={key:0,class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},A={key:1},G={class:"flex justify-between mt-2"},H={class:"max-w-md mx-auto px-4 py-6"},I={class:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4"},J={class:"max-w-md mx-auto flex space-x-4"},K=["disabled"],W=F({__name:"BookingLayout",setup(O){const i=N(),o=j(),r=V(),c=l(()=>[{name:"staff",label:"選擇人員",completed:!!r.bookingForm.staff_id,active:o.name==="booking-staff"},{name:"service",label:"選擇服務",completed:!!r.bookingForm.service_id,active:o.name==="booking-service"},{name:"datetime",label:"選擇時間",completed:!!r.bookingForm.booking_date&&!!r.bookingForm.booking_time,active:o.name==="booking-datetime"},{name:"confirm",label:"確認預約",completed:!1,active:o.name==="booking-confirm"}]),h=l(()=>c.value.find(t=>t.active)?.label||"預約服務"),p=(d,t)=>d.completed||d.active?"bg-line-green text-white":"bg-gray-200 text-gray-600",_=l(()=>o.name!=="booking-staff"),y=l(()=>o.name!=="booking-confirm"),w=l(()=>{switch(o.name){case"booking-staff":return"選擇服務";case"booking-service":return"選擇時間";case"booking-datetime":return"確認預約";default:return"下一步"}}),f=l(()=>{switch(o.name){case"booking-staff":return!!r.bookingForm.staff_id;case"booking-service":return!!r.bookingForm.service_id;case"booking-datetime":return!!r.bookingForm.booking_date&&!!r.bookingForm.booking_time;default:return!1}}),k=()=>{switch(o.name){case"booking-service":i.push("/booking/staff");break;case"booking-datetime":i.push("/booking/service");break;case"booking-confirm":i.push("/booking/datetime");break;default:i.push("/")}},B=()=>{if(f.value)switch(o.name){case"booking-staff":i.push("/booking/service");break;case"booking-service":i.push("/booking/datetime");break;case"booking-datetime":i.push("/booking/confirm");break}};return(d,t)=>{const C=L("router-view");return n(),s("div",M,[e("header",R,[e("div",T,[e("button",{onClick:k,class:"w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900"},t[1]||(t[1]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)])),e("h1",D,u(h.value),1),e("button",{onClick:t[0]||(t[0]=a=>S(i).push("/")),class:"text-sm text-gray-600 hover:text-gray-900"}," 取消 ")])]),e("div",E,[e("div",P,[e("div",$,[(n(!0),s(v,null,x(c.value,(a,b)=>(n(),s("div",{key:a.name,class:m(["flex items-center",{"flex-1":b<c.value.length-1}])},[e("div",{class:m(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",p(a)])},[a.completed?(n(),s("svg",q,t[2]||(t[2]=[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"},null,-1)]))):(n(),s("span",A,u(b+1),1))],2),b<c.value.length-1?(n(),s("div",{key:0,class:m(["flex-1 h-0.5 mx-2",a.completed?"bg-line-green":"bg-gray-200"])},null,2)):g("",!0)],2))),128))]),e("div",G,[(n(!0),s(v,null,x(c.value,a=>(n(),s("div",{key:a.name,class:m(["text-xs text-center",a.active?"text-line-green font-medium":"text-gray-500"])},u(a.label),3))),128))])])]),e("main",H,[z(C)]),e("div",I,[e("div",J,[_.value?(n(),s("button",{key:0,onClick:k,class:"btn-secondary flex-1"}," 上一步 ")):g("",!0),y.value?(n(),s("button",{key:1,onClick:B,disabled:!f.value,class:"btn-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed"},u(w.value),9,K)):g("",!0)])]),t[3]||(t[3]=e("div",{class:"h-20"},null,-1))])}}});export{W as default};
