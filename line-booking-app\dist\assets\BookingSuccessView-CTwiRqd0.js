import{d as x,g as r,j as g,h as f,e as b,c as v,a as e,b as o,f as a,t as n,o as p}from"./index-CK6tErNr.js";import{u as y}from"./booking-CCzFsPCM.js";const w={class:"min-h-screen bg-gray-50"},h={class:"bg-white shadow-sm"},k={class:"max-w-md mx-auto px-4 py-4 flex items-center justify-between"},_={class:"max-w-md mx-auto px-4 py-6"},S={class:"card mb-6"},j={class:"space-y-4"},B={class:"flex justify-between"},C={class:"font-medium text-gray-900"},$={class:"flex justify-between"},z={class:"font-medium text-gray-900"},D={class:"flex justify-between"},F={class:"font-medium text-gray-900"},L={class:"flex justify-between"},M={class:"font-medium text-gray-900"},N={class:"flex justify-between"},I={class:"font-medium text-gray-900"},T={class:"border-t border-gray-200 pt-4"},V={class:"flex justify-between text-lg font-bold"},E={class:"text-line-green"},R={class:"space-y-4"},W=x({__name:"BookingSuccessView",setup(q){const l=b(),d=g(),s=y(),m=r(()=>d.query.bookingId),u=r(()=>!s.bookingForm.booking_date||!s.bookingForm.booking_time?"":`${new Date(s.bookingForm.booking_date).toLocaleDateString("zh-TW",{year:"numeric",month:"long",day:"numeric",weekday:"long"})} ${s.bookingForm.booking_time}`);return f(()=>{(!s.selectedStaff||!s.selectedService)&&l.replace("/")}),(c,t)=>(p(),v("div",w,[e("header",h,[e("div",k,[t[4]||(t[4]=e("h1",{class:"text-lg font-semibold text-gray-900"},"預約成功",-1)),e("button",{onClick:t[0]||(t[0]=i=>o(l).push("/")),class:"text-sm text-gray-600 hover:text-gray-900"}," 回首頁 ")])]),e("main",_,[t[12]||(t[12]=a('<div class="text-center mb-8"><div class="w-20 h-20 bg-green-100 rounded-full mx-auto mb-4 flex items-center justify-center"><svg class="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg></div><h2 class="text-2xl font-bold text-gray-900 mb-2">預約成功！</h2><p class="text-gray-600">您的預約已成功提交，我們將盡快為您確認</p></div>',1)),e("div",S,[t[11]||(t[11]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-semibold text-gray-900"},"預約詳情"),e("span",{class:"px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-medium rounded-full"}," 待確認 ")],-1)),e("div",j,[e("div",B,[t[5]||(t[5]=e("span",{class:"text-gray-600"},"預約編號",-1)),e("span",C,"#"+n(m.value||"B"+Date.now().toString().slice(-6)),1)]),e("div",$,[t[6]||(t[6]=e("span",{class:"text-gray-600"},"服務人員",-1)),e("span",z,n(o(s).selectedStaff?.name),1)]),e("div",D,[t[7]||(t[7]=e("span",{class:"text-gray-600"},"服務項目",-1)),e("span",F,n(o(s).selectedService?.name),1)]),e("div",L,[t[8]||(t[8]=e("span",{class:"text-gray-600"},"預約時間",-1)),e("span",M,n(u.value),1)]),e("div",N,[t[9]||(t[9]=e("span",{class:"text-gray-600"},"服務時長",-1)),e("span",I,n(o(s).selectedService?.duration)+" 分鐘",1)]),e("div",T,[e("div",V,[t[10]||(t[10]=e("span",null,"總費用",-1)),e("span",E,"NT$ "+n(o(s).finalPrice.toLocaleString()),1)])])])]),t[13]||(t[13]=a('<div class="card mb-6 bg-blue-50 border border-blue-200"><div class="flex items-start space-x-3"><div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"><svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg></div><div><h4 class="font-medium text-gray-900 mb-2">重要提醒</h4><ul class="text-sm text-gray-600 space-y-1"><li>• 我們將透過 LINE 訊息通知您預約確認結果</li><li>• 請於預約時間前 10 分鐘到達</li><li>• 如需取消或變更，請提前 24 小時聯絡我們</li><li>• 遲到超過 15 分鐘可能需要重新安排時間</li></ul></div></div></div>',1)),e("div",R,[e("button",{onClick:t[1]||(t[1]=i=>o(l).push("/member/bookings")),class:"btn-primary w-full"}," 查看預約記錄 "),e("button",{onClick:t[2]||(t[2]=i=>o(l).push("/booking")),class:"btn-secondary w-full"}," 再次預約 "),e("button",{onClick:t[3]||(t[3]=i=>o(l).push("/")),class:"w-full text-gray-600 hover:text-gray-900 font-medium py-3"}," 回到首頁 ")]),t[14]||(t[14]=a('<div class="mt-8 text-center"><p class="text-sm text-gray-500 mb-2">如有任何問題，請聯絡我們</p><div class="flex justify-center space-x-6 text-sm"><a href="tel:+886-2-1234-5678" class="text-line-green hover:underline"> 📞 (02) 1234-5678 </a><a href="mailto:<EMAIL>" class="text-line-green hover:underline"> ✉️ 客服信箱 </a></div></div>',1))])]))}});export{W as default};
