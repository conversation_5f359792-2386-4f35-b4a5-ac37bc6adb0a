import{d as S,u as V,r as f,g as z,h as $,c as a,a as e,F as u,k as v,t as r,p as h,f as H,i as p,e as j,o}from"./index-CK6tErNr.js";import{u as D}from"./booking-CCzFsPCM.js";const L={class:"space-y-6"},M={class:"card"},N={class:"flex space-x-2"},T=["onClick"],F={key:0,class:"space-y-4"},O={key:1,class:"space-y-4"},A={class:"flex justify-between items-start mb-4"},E={class:"flex-1"},R={class:"flex items-center space-x-2 mb-2"},U={class:"font-semibold text-gray-900"},W={class:"space-y-1 text-sm text-gray-600"},q={class:"flex items-center"},G={class:"flex items-center"},I={class:"flex items-center"},J={class:"text-right"},K={class:"text-lg font-bold text-line-green mb-2"},P=["onClick"],Q={key:0,class:"flex space-x-3 pt-4 border-t border-gray-200"},X=["onClick"],Y=["onClick"],Z={key:2,class:"card text-center py-12"},ee={class:"text-lg font-semibold text-gray-900 mb-2"},te={class:"text-gray-600 mb-6"},oe=S({__name:"BookingsView",setup(se){j();const i=D(),g=V(),c=f(!1),n=f("all"),y=[{value:"all",label:"全部"},{value:"pending",label:"待確認"},{value:"confirmed",label:"已確認"},{value:"completed",label:"已完成"},{value:"cancelled",label:"已取消"}],m=z(()=>n.value==="all"?i.userBookings:i.userBookings.filter(s=>s.status===n.value)),b=s=>({pending:"bg-yellow-100 text-yellow-800",confirmed:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",x=s=>({all:"全部",pending:"待確認",confirmed:"已確認",completed:"已完成",cancelled:"已取消"})[s]||s,_=(s,l)=>`${new Date(s).toLocaleDateString("zh-TW",{month:"short",day:"numeric",weekday:"short"})} ${l}`,w=s=>{console.log("查看預約詳情:",s)},k=s=>{console.log("重新安排預約:",s)},B=s=>{confirm("確定要取消這個預約嗎？")&&console.log("取消預約:",s)},C=async()=>{if(g.user){c.value=!0;try{await i.fetchUserBookings(g.user.id)}catch(s){console.error("載入預約記錄失敗:",s)}finally{c.value=!1}}};return $(()=>{C()}),(s,l)=>(o(),a("div",L,[l[6]||(l[6]=e("div",null,[e("h2",{class:"text-xl font-bold text-gray-900 mb-2"},"預約記錄"),e("p",{class:"text-gray-600"},"查看您的所有預約記錄")],-1)),e("div",M,[e("div",N,[(o(),a(u,null,v(y,t=>e("button",{key:t.value,onClick:d=>n.value=t.value,class:h(["px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200",n.value===t.value?"bg-line-green text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"])},r(t.label),11,T)),64))])]),c.value?(o(),a("div",F,[(o(),a(u,null,v(3,t=>e("div",{key:t,class:"card animate-pulse"},l[1]||(l[1]=[H('<div class="flex justify-between items-start"><div class="flex-1"><div class="h-4 bg-gray-200 rounded mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3 mb-2"></div><div class="h-3 bg-gray-200 rounded w-1/3"></div></div><div class="h-6 bg-gray-200 rounded w-16"></div></div>',1)]))),64))])):m.value.length>0?(o(),a("div",O,[(o(!0),a(u,null,v(m.value,t=>(o(),a("div",{key:t.id,class:"card hover:shadow-lg transition-shadow duration-200"},[e("div",A,[e("div",E,[e("div",R,[e("h3",U,r(t.service?.name),1),e("span",{class:h(["px-2 py-1 text-xs font-medium rounded-full",b(t.status)])},r(x(t.status)),3)]),e("div",W,[e("div",q,[l[2]||(l[2]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)),e("span",null,r(t.staff?.name),1)]),e("div",G,[l[3]||(l[3]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),e("span",null,r(_(t.booking_date,t.booking_time)),1)]),e("div",I,[l[4]||(l[4]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),e("span",null,r(t.service?.duration)+" 分鐘",1)])])]),e("div",J,[e("div",K," NT$ "+r(t.final_price.toLocaleString()),1),e("button",{onClick:d=>w(t),class:"text-sm text-gray-500 hover:text-gray-700"}," 查看詳情 ",8,P)])]),t.status==="pending"||t.status==="confirmed"?(o(),a("div",Q,[t.status==="confirmed"?(o(),a("button",{key:0,onClick:d=>k(t),class:"flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 text-sm"}," 重新安排 ",8,X)):p("",!0),e("button",{onClick:d=>B(t),class:"flex-1 py-2 px-4 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 text-sm"}," 取消預約 ",8,Y)])):p("",!0)]))),128))])):(o(),a("div",Z,[l[5]||(l[5]=e("div",{class:"w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center"},[e("svg",{class:"w-8 h-8 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})])],-1)),e("h3",ee,r(n.value==="all"?"暫無預約記錄":`暫無${x(n.value)}記錄`),1),e("p",te,r(n.value==="all"?"您還沒有任何預約記錄":"切換其他狀態查看更多記錄"),1),e("button",{onClick:l[0]||(l[0]=t=>s.$router.push("/booking")),class:"btn-primary"}," 立即預約 ")]))]))}});export{oe as default};
