import{u as I}from"./booking-BzgRkTr2.js";import{d as P,r as v,g as h,w as Y,h as W,c as n,a as t,i as M,t as i,b as m,F as f,k as b,f as q,p as T,o}from"./index-8Cki6B3T.js";const A={class:"card mb-6 bg-blue-50 border border-blue-200"},E={class:"space-y-2"},H={class:"flex items-center justify-between"},O={class:"font-medium text-gray-900"},G={class:"flex items-center justify-between"},J={class:"font-medium text-gray-900"},K={class:"flex items-center justify-between"},Q={class:"font-medium text-gray-900"},R={class:"flex items-center justify-between"},U={class:"font-medium text-line-green"},X={class:"card mb-6"},Z={class:"flex items-center justify-between mb-4"},ee={class:"text-lg font-semibold text-gray-900"},te={class:"grid grid-cols-7 gap-1 mb-4"},se=["onClick","disabled"],ae={key:0,class:"card"},re={class:"text-lg font-semibold text-gray-900 mb-4"},ne={key:0,class:"grid grid-cols-3 gap-3"},oe={key:1,class:"grid grid-cols-3 gap-3"},le=["onClick","disabled"],ie={key:2,class:"text-center py-8"},de={key:1,class:"mt-6 card bg-green-50 border border-green-200"},ce={class:"flex items-center space-x-3"},ue={class:"font-medium text-gray-900"},fe=P({__name:"DateTimeSelectionView",setup(ge){const r=I(),d=v(new Date),l=v(""),c=v(""),y=v(!1),j=["日","一","二","三","四","五","六"],p=h(()=>l.value?new Date(l.value).toLocaleDateString("zh-TW",{month:"long",day:"numeric",weekday:"short"}):""),_=h(()=>l.value?r.availableDates.find(e=>e.date===l.value)?.time_slots||[]:[]),$=h(()=>{const s=d.value.getFullYear(),e=d.value.getMonth(),a=new Date(s,e,1),u=new Date(a);u.setDate(u.getDate()-a.getDay());const w=[],k=new Date;k.setHours(0,0,0,0);for(let x=0;x<42;x++){const g=new Date(u);g.setDate(u.getDate()+x);const D=g.toISOString().split("T")[0],C=g.getMonth()===e,S=g<k;w.push({day:g.getDate(),dateString:D,isCurrentMonth:C,isPast:S,available:C&&!S,isSelected:D===l.value})}return w}),F=()=>{d.value=new Date(d.value.getFullYear(),d.value.getMonth()-1,1)},L=()=>{d.value=new Date(d.value.getFullYear(),d.value.getMonth()+1,1)},z=async s=>{l.value=s,c.value="",r.selectedStaff&&(y.value=!0,await r.checkAvailability(r.selectedStaff.id,s),y.value=!1)},B=s=>{c.value=s,r.setDateTime(l.value,s)},V=s=>{const e="border";return s.isCurrentMonth?s.isPast||!s.available?`${e} text-gray-400 border-gray-200 cursor-not-allowed`:s.isSelected?`${e} bg-line-green text-white border-line-green`:`${e} text-gray-900 border-gray-200 hover:border-line-green hover:bg-green-50`:`${e} text-gray-300 border-transparent cursor-not-allowed`},N=s=>{const e="border";return s.available?s.time===c.value?`${e} bg-line-green text-white border-line-green`:`${e} text-gray-900 border-gray-200 hover:border-line-green hover:bg-green-50`:`${e} text-gray-400 border-gray-200 bg-gray-50 cursor-not-allowed`};return Y([l,c],([s,e])=>{s&&e&&r.setDateTime(s,e)}),W(()=>{r.bookingForm.booking_date&&(l.value=r.bookingForm.booking_date),r.bookingForm.booking_time&&(c.value=r.bookingForm.booking_time)}),(s,e)=>(o(),n("div",null,[e[10]||(e[10]=t("div",{class:"mb-6"},[t("h2",{class:"text-xl font-bold text-gray-900 mb-2"},"選擇日期時間"),t("p",{class:"text-gray-600"},"請選擇您希望預約的日期和時間")],-1)),t("div",A,[t("div",E,[t("div",H,[e[0]||(e[0]=t("span",{class:"text-sm text-gray-600"},"服務人員",-1)),t("span",O,i(m(r).selectedStaff?.name),1)]),t("div",G,[e[1]||(e[1]=t("span",{class:"text-sm text-gray-600"},"服務項目",-1)),t("span",J,i(m(r).selectedService?.name),1)]),t("div",K,[e[2]||(e[2]=t("span",{class:"text-sm text-gray-600"},"服務時長",-1)),t("span",Q,i(m(r).selectedService?.duration)+" 分鐘",1)]),t("div",R,[e[3]||(e[3]=t("span",{class:"text-sm text-gray-600"},"服務費用",-1)),t("span",U,"NT$ "+i(m(r).selectedService?.price.toLocaleString()),1)])])]),t("div",X,[e[6]||(e[6]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"選擇日期",-1)),t("div",Z,[t("button",{onClick:F,class:"w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"},e[4]||(e[4]=[t("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)])),t("h4",ee,i(d.value.toLocaleDateString("zh-TW",{year:"numeric",month:"long"})),1),t("button",{onClick:L,class:"w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"},e[5]||(e[5]=[t("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))]),t("div",te,[(o(),n(f,null,b(j,a=>t("div",{key:a,class:"text-center text-sm font-medium text-gray-500 py-2"},i(a),1)),64)),(o(!0),n(f,null,b($.value,a=>(o(),n("button",{key:a.dateString,onClick:u=>z(a.dateString),disabled:!a.available||a.isPast,class:T(["aspect-square flex items-center justify-center text-sm rounded-lg transition-colors duration-200",V(a)])},i(a.day),11,se))),128))])]),l.value?(o(),n("div",ae,[t("h3",re," 選擇時間 - "+i(p.value),1),y.value?(o(),n("div",ne,[(o(),n(f,null,b(6,a=>t("div",{key:a,class:"h-12 bg-gray-200 rounded-lg animate-pulse"})),64))])):_.value.length>0?(o(),n("div",oe,[(o(!0),n(f,null,b(_.value,a=>(o(),n("button",{key:a.time,onClick:u=>B(a.time),disabled:!a.available,class:T(["h-12 flex items-center justify-center text-sm font-medium rounded-lg border transition-colors duration-200",N(a)])},i(a.time),11,le))),128))])):(o(),n("div",ie,e[7]||(e[7]=[q('<div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center"><svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg></div><h4 class="text-lg font-semibold text-gray-900 mb-2">該日期無可用時間</h4><p class="text-gray-600">請選擇其他日期</p>',3)])))])):M("",!0),l.value&&c.value?(o(),n("div",de,[t("div",ce,[e[9]||(e[9]=t("div",{class:"w-8 h-8 bg-line-green rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})])],-1)),t("div",null,[t("p",ue,"預約時間："+i(p.value)+" "+i(c.value),1),e[8]||(e[8]=t("p",{class:"text-sm text-gray-600"},"請確認時間無誤後進行下一步",-1))])])])):M("",!0)]))}});export{fe as default};
