import{d as u,u as v,c as i,a as e,b as l,e as c,f as n,o as r}from"./index-CK6tErNr.js";const m={class:"min-h-screen bg-gray-50"},x={class:"bg-white shadow-sm"},h={class:"max-w-md mx-auto px-4 py-4 flex items-center justify-between"},f={class:"flex items-center space-x-4"},p={class:"max-w-md mx-auto px-4 py-6"},g={class:"grid grid-cols-2 gap-4"},k=u({__name:"HomeView",setup(b){const s=c(),o=v(),d=()=>{o.isAuthenticated?s.push("/booking"):s.push("/login")};return(w,t)=>(r(),i("div",m,[e("header",x,[e("div",h,[t[5]||(t[5]=e("h1",{class:"text-xl font-bold text-gray-900"},"線上預約",-1)),e("div",f,[l(o).isAuthenticated?(r(),i("button",{key:1,onClick:t[1]||(t[1]=a=>l(s).push("/member")),class:"w-8 h-8 bg-line-green rounded-full flex items-center justify-center"},t[4]||(t[4]=[e("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)]))):(r(),i("button",{key:0,onClick:t[0]||(t[0]=a=>l(s).push("/login")),class:"text-line-green hover:text-green-600 font-medium"}," 登入 "))])])]),e("main",p,[e("div",{class:"card mb-6"},[e("div",{class:"text-center"},[t[6]||(t[6]=n('<div class="w-16 h-16 bg-gradient-to-br from-line-green to-green-600 rounded-full mx-auto mb-4 flex items-center justify-center"><svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg></div><h2 class="text-2xl font-bold text-gray-900 mb-2">歡迎使用預約系統</h2><p class="text-gray-600 mb-6">快速預約您需要的服務</p>',3)),e("button",{onClick:d,class:"btn-primary w-full"}," 開始預約 ")])]),t[9]||(t[9]=n('<div class="card mb-6"><h3 class="text-lg font-semibold text-gray-900 mb-4">服務特色</h3><div class="space-y-4"><div class="flex items-start space-x-3"><div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0"><svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg></div><div><h4 class="font-medium text-gray-900">專業服務團隊</h4><p class="text-sm text-gray-600">經驗豐富的專業人員為您提供優質服務</p></div></div><div class="flex items-start space-x-3"><div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0"><svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg></div><div><h4 class="font-medium text-gray-900">彈性預約時間</h4><p class="text-sm text-gray-600">多種時間段選擇，配合您的行程安排</p></div></div><div class="flex items-start space-x-3"><div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0"><svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20"><path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"></path></svg></div><div><h4 class="font-medium text-gray-900">票券優惠系統</h4><p class="text-sm text-gray-600">使用票券享受更多優惠折扣</p></div></div></div></div>',1)),e("div",g,[e("button",{onClick:t[2]||(t[2]=a=>l(s).push("/member/bookings")),class:"card p-4 text-center hover:shadow-lg transition-shadow duration-200"},t[7]||(t[7]=[e("div",{class:"w-12 h-12 bg-orange-100 rounded-full mx-auto mb-3 flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})])],-1),e("h4",{class:"font-medium text-gray-900 text-sm"},"預約記錄",-1)])),e("button",{onClick:t[3]||(t[3]=a=>l(s).push("/member/tickets")),class:"card p-4 text-center hover:shadow-lg transition-shadow duration-200"},t[8]||(t[8]=[e("div",{class:"w-12 h-12 bg-pink-100 rounded-full mx-auto mb-3 flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-pink-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"})])],-1),e("h4",{class:"font-medium text-gray-900 text-sm"},"我的票券",-1)]))])])]))}});export{k as default};
