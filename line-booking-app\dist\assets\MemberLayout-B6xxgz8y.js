import{d as f,u as h,c as n,a as e,b as s,e as v,t as a,i as w,m as l,s as c,p as u,j as k,n as g,o as i,x}from"./index-8Cki6B3T.js";const C={class:"min-h-screen bg-gray-50"},j={class:"bg-white shadow-sm"},B={class:"max-w-md mx-auto px-4 py-4 flex items-center justify-between"},V={class:"bg-white border-b border-gray-200"},z={class:"max-w-md mx-auto px-4 py-6"},L={class:"flex items-center space-x-4"},M={class:"relative"},N=["src","alt"],S={key:1,class:"w-16 h-16 bg-gradient-to-br from-line-green to-green-600 rounded-full flex items-center justify-center"},$={class:"text-white font-semibold text-xl"},A={class:"flex-1 min-w-0"},R={class:"text-lg font-semibold text-gray-900 truncate"},D={key:0,class:"text-sm text-gray-600 truncate"},E={class:"text-xs text-gray-500 mt-1"},O={class:"bg-white border-b border-gray-200"},T={class:"max-w-md mx-auto"},U={class:"flex"},q={class:"max-w-md mx-auto px-4 py-6"},F={class:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4"},G={class:"max-w-md mx-auto"},J=f({__name:"MemberLayout",setup(H){const r=v(),d=k(),o=h(),p=async()=>{try{await o.signOut(),r.push("/")}catch(_){console.error("登出失敗:",_)}};return(_,t)=>{const m=g("router-link"),y=g("router-view");return i(),n("div",C,[e("header",j,[e("div",B,[e("button",{onClick:t[0]||(t[0]=b=>s(r).push("/")),class:"w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900"},t[3]||(t[3]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)])),t[4]||(t[4]=e("h1",{class:"text-lg font-semibold text-gray-900"},"會員中心",-1)),e("button",{onClick:p,class:"text-sm text-gray-600 hover:text-gray-900"}," 登出 ")])]),e("div",V,[e("div",z,[e("div",L,[e("div",M,[s(o).user?.picture_url?(i(),n("img",{key:0,src:s(o).user.picture_url,alt:s(o).user.display_name,class:"w-16 h-16 rounded-full object-cover"},null,8,N)):(i(),n("div",S,[e("span",$,a(s(o).user?.display_name?.charAt(0)||"U"),1)]))]),e("div",A,[e("h2",R,a(s(o).user?.display_name||"用戶"),1),s(o).user?.email?(i(),n("p",D,a(s(o).user.email),1)):w("",!0),e("p",E," 會員編號："+a(s(o).user?.id?.slice(-8)),1)]),e("button",{onClick:t[1]||(t[1]=b=>s(r).push("/member")),class:"w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600"},t[5]||(t[5]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))])])]),e("div",O,[e("div",T,[e("nav",U,[l(m,{to:"/member",class:u(["flex-1 py-4 px-4 text-center text-sm font-medium transition-colors duration-200",s(d).name==="member-profile"?"text-line-green border-b-2 border-line-green":"text-gray-500 hover:text-gray-700"])},{default:c(()=>t[6]||(t[6]=[x(" 個人資料 ")])),_:1,__:[6]},8,["class"]),l(m,{to:"/member/bookings",class:u(["flex-1 py-4 px-4 text-center text-sm font-medium transition-colors duration-200",s(d).name==="member-bookings"?"text-line-green border-b-2 border-line-green":"text-gray-500 hover:text-gray-700"])},{default:c(()=>t[7]||(t[7]=[x(" 預約記錄 ")])),_:1,__:[7]},8,["class"]),l(m,{to:"/member/tickets",class:u(["flex-1 py-4 px-4 text-center text-sm font-medium transition-colors duration-200",s(d).name==="member-tickets"?"text-line-green border-b-2 border-line-green":"text-gray-500 hover:text-gray-700"])},{default:c(()=>t[8]||(t[8]=[x(" 我的票券 ")])),_:1,__:[8]},8,["class"])])])]),e("main",q,[l(y)]),e("div",F,[e("div",G,[e("button",{onClick:t[2]||(t[2]=b=>s(r).push("/booking")),class:"btn-primary w-full"}," 立即預約 ")])]),t[9]||(t[9]=e("div",{class:"h-20"},null,-1))])}}});export{J as default};
