import{d,c as m,a as e,f as x,b as a,e as u,m as o,s as n,n as c,x as r,o as p}from"./index-CK6tErNr.js";const v={class:"min-h-screen bg-gray-50 flex items-center justify-center p-4"},b={class:"text-center"},f={class:"space-y-4"},_={class:"mt-8"},g={class:"flex justify-center space-x-6 text-sm"},h=d({__name:"NotFoundView",setup(y){const l=u();return(k,t)=>{const s=c("router-link");return p(),m("div",v,[e("div",b,[t[6]||(t[6]=x('<div class="mb-8"><div class="w-32 h-32 mx-auto mb-6 text-gray-300"><svg fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg></div><h1 class="text-6xl font-bold text-gray-400 mb-4">404</h1><h2 class="text-2xl font-semibold text-gray-900 mb-2">頁面不存在</h2><p class="text-gray-600 mb-8">抱歉，您要找的頁面不存在或已被移除</p></div>',1)),e("div",f,[e("button",{onClick:t[0]||(t[0]=i=>a(l).push("/")),class:"btn-primary"}," 回到首頁 "),e("button",{onClick:t[1]||(t[1]=i=>a(l).back()),class:"btn-secondary"}," 返回上頁 ")]),e("div",_,[t[5]||(t[5]=e("p",{class:"text-sm text-gray-500 mb-4"},"您可能在找：",-1)),e("div",g,[o(s,{to:"/booking",class:"text-line-green hover:underline"},{default:n(()=>t[2]||(t[2]=[r(" 立即預約 ")])),_:1,__:[2]}),o(s,{to:"/member",class:"text-line-green hover:underline"},{default:n(()=>t[3]||(t[3]=[r(" 會員中心 ")])),_:1,__:[3]}),o(s,{to:"/login",class:"text-line-green hover:underline"},{default:n(()=>t[4]||(t[4]=[r(" 登入 ")])),_:1,__:[4]})])])])])}}});export{h as default};
