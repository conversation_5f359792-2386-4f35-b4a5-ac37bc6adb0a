import{d as h,u as B,y as b,r as k,h as j,c as m,a as t,i as D,z as w,q as f,v as c,t as r,b as p,p as x,o as g}from"./index-8Cki6B3T.js";const V={class:"space-y-6"},N={class:"card"},S=["disabled"],U={key:0,class:"flex items-center justify-center space-x-2"},$={key:1},z={class:"card"},P={class:"space-y-3"},E={class:"flex justify-between"},I={class:"font-medium text-gray-900"},L={class:"flex justify-between"},M={class:"font-medium text-gray-900"},A={class:"flex justify-between"},q={class:"font-medium text-gray-900"},F={class:"flex justify-between"},O={class:"font-medium text-gray-900"},W={class:"card"},G={class:"grid grid-cols-2 gap-4"},H={class:"text-center p-4 bg-blue-50 rounded-lg"},J={class:"text-2xl font-bold text-blue-600 mb-1"},K={class:"text-center p-4 bg-green-50 rounded-lg"},Q={class:"text-2xl font-bold text-green-600 mb-1"},R={class:"text-center p-4 bg-purple-50 rounded-lg"},X={class:"text-2xl font-bold text-purple-600 mb-1"},Y={class:"text-center p-4 bg-orange-50 rounded-lg"},Z={class:"text-2xl font-bold text-orange-600 mb-1"},tt={class:"card"},et={class:"space-y-4"},st={class:"flex items-center justify-between"},ot={class:"flex items-center justify-between"},lt={class:"card border border-red-200"},nt={class:"space-y-3"},it={class:"flex space-x-3"},dt=h({__name:"ProfileView",setup(rt){const n=B(),o=b({display_name:"",email:"",phone:""}),u=k(!1),d=k(!1),i=b({totalBookings:0,completedBookings:0,availableTickets:0,usedTickets:0}),a=b({booking_reminder:!0,promotion_notification:!0}),y=s=>s?new Date(s).toLocaleDateString("zh-TW"):"-",_=async()=>{u.value=!0;try{console.log("更新個人資料:",o),await new Promise(s=>setTimeout(s,1e3)),alert("個人資料更新成功！")}catch(s){console.error("更新失敗:",s),alert("更新失敗，請稍後再試")}finally{u.value=!1}},v=s=>{a[s]=!a[s],console.log("偏好設定更新:",a)},C=async()=>{try{console.log("刪除帳號"),await new Promise(s=>setTimeout(s,1e3)),await n.signOut(),window.location.href="/"}catch(s){console.error("刪除帳號失敗:",s),alert("刪除失敗，請稍後再試")}d.value=!1},T=async()=>{try{i.totalBookings=12,i.completedBookings=10,i.availableTickets=3,i.usedTickets=5}catch(s){console.error("載入統計資料失敗:",s)}};return j(()=>{n.user&&(o.display_name=n.user.display_name||"",o.email=n.user.email||"",o.phone=n.user.phone||""),T()}),(s,e)=>(g(),m("div",V,[t("div",N,[e[13]||(e[13]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"個人資料",-1)),t("form",{onSubmit:w(_,["prevent"]),class:"space-y-4"},[t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 顯示名稱 ",-1)),f(t("input",{"onUpdate:modelValue":e[0]||(e[0]=l=>o.display_name=l),type:"text",class:"input-field",placeholder:"請輸入您的顯示名稱"},null,512),[[c,o.display_name]])]),t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 電子郵件 ",-1)),f(t("input",{"onUpdate:modelValue":e[1]||(e[1]=l=>o.email=l),type:"email",class:"input-field",placeholder:"請輸入您的電子郵件"},null,512),[[c,o.email]])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 手機號碼 ",-1)),f(t("input",{"onUpdate:modelValue":e[2]||(e[2]=l=>o.phone=l),type:"tel",class:"input-field",placeholder:"請輸入您的手機號碼"},null,512),[[c,o.phone]])]),t("button",{type:"submit",disabled:u.value,class:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"},[u.value?(g(),m("div",U,e[12]||(e[12]=[t("div",{class:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"},null,-1),t("span",null,"更新中...",-1)]))):(g(),m("span",$,"儲存變更"))],8,S)],32)]),t("div",z,[e[18]||(e[18]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"帳號資訊",-1)),t("div",P,[t("div",E,[e[14]||(e[14]=t("span",{class:"text-gray-600"},"會員編號",-1)),t("span",I,r(p(n).user?.id?.slice(-8)),1)]),t("div",L,[e[15]||(e[15]=t("span",{class:"text-gray-600"},"LINE 用戶 ID",-1)),t("span",M,r(p(n).user?.line_user_id?.slice(-8)),1)]),t("div",A,[e[16]||(e[16]=t("span",{class:"text-gray-600"},"註冊時間",-1)),t("span",q,r(y(p(n).user?.created_at)),1)]),t("div",F,[e[17]||(e[17]=t("span",{class:"text-gray-600"},"最後更新",-1)),t("span",O,r(y(p(n).user?.updated_at)),1)])])]),t("div",W,[e[23]||(e[23]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"使用統計",-1)),t("div",G,[t("div",H,[t("div",J,r(i.totalBookings),1),e[19]||(e[19]=t("div",{class:"text-sm text-gray-600"},"總預約次數",-1))]),t("div",K,[t("div",Q,r(i.completedBookings),1),e[20]||(e[20]=t("div",{class:"text-sm text-gray-600"},"完成預約",-1))]),t("div",R,[t("div",X,r(i.availableTickets),1),e[21]||(e[21]=t("div",{class:"text-sm text-gray-600"},"可用票券",-1))]),t("div",Y,[t("div",Z,r(i.usedTickets),1),e[22]||(e[22]=t("div",{class:"text-sm text-gray-600"},"已用票券",-1))])])]),t("div",tt,[e[26]||(e[26]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"偏好設定",-1)),t("div",et,[t("div",st,[e[24]||(e[24]=t("div",null,[t("p",{class:"font-medium text-gray-900"},"預約提醒"),t("p",{class:"text-sm text-gray-600"},"在預約前 1 小時發送 LINE 提醒")],-1)),t("button",{onClick:e[3]||(e[3]=l=>v("booking_reminder")),class:x(["relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200",a.booking_reminder?"bg-line-green":"bg-gray-200"])},[t("span",{class:x(["inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200",a.booking_reminder?"translate-x-6":"translate-x-1"])},null,2)],2)]),t("div",ot,[e[25]||(e[25]=t("div",null,[t("p",{class:"font-medium text-gray-900"},"優惠通知"),t("p",{class:"text-sm text-gray-600"},"接收新票券和優惠活動通知")],-1)),t("button",{onClick:e[4]||(e[4]=l=>v("promotion_notification")),class:x(["relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200",a.promotion_notification?"bg-line-green":"bg-gray-200"])},[t("span",{class:x(["inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200",a.promotion_notification?"translate-x-6":"translate-x-1"])},null,2)],2)])])]),t("div",lt,[e[28]||(e[28]=t("h3",{class:"text-lg font-semibold text-red-600 mb-4"},"危險操作",-1)),t("div",nt,[t("button",{onClick:e[5]||(e[5]=l=>d.value=!0),class:"w-full py-3 px-4 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200"}," 刪除帳號 "),e[27]||(e[27]=t("p",{class:"text-xs text-gray-500"}," 刪除帳號將永久移除您的所有資料，此操作無法復原 ",-1))])]),d.value?(g(),m("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:e[8]||(e[8]=l=>d.value=!1)},[t("div",{class:"bg-white rounded-lg p-6 max-w-sm w-full",onClick:e[7]||(e[7]=w(()=>{},["stop"]))},[e[29]||(e[29]=t("h4",{class:"text-lg font-semibold text-gray-900 mb-2"},"確認刪除帳號",-1)),e[30]||(e[30]=t("p",{class:"text-gray-600 mb-4"}," 此操作將永久刪除您的帳號和所有相關資料，包括預約記錄和票券。此操作無法復原。 ",-1)),t("div",it,[t("button",{onClick:e[6]||(e[6]=l=>d.value=!1),class:"flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"}," 取消 "),t("button",{onClick:C,class:"flex-1 py-2 px-4 bg-red-600 text-white rounded-lg hover:bg-red-700"}," 確認刪除 ")])])])):D("",!0)]))}});export{dt as default};
