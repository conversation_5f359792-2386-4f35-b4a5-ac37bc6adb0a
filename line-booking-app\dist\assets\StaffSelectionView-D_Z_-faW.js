import{u as x}from"./booking-BzgRkTr2.js";import{d as f,g as p,h as b,c as l,a as e,i,b as o,F as v,k as u,t as d,f as m,o as a,p as h,_ as y}from"./index-8Cki6B3T.js";const _={key:0,class:"space-y-4"},w={key:1,class:"card text-center py-8"},k={class:"text-gray-600 mb-4"},S={key:2,class:"space-y-4"},C=["onClick"],M={class:"flex items-center space-x-4"},z={class:"relative"},B=["src","alt"],V={key:1,class:"w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center"},j={class:"text-white font-semibold text-lg"},L={key:2,class:"absolute -bottom-1 -right-1 w-6 h-6 bg-line-green rounded-full flex items-center justify-center"},F={class:"flex-1 min-w-0"},N={class:"font-semibold text-gray-900 mb-1"},I={key:0,class:"text-sm text-line-green mb-1"},$={key:1,class:"text-sm text-gray-600 line-clamp-2"},A={key:2,class:"mt-2"},D={key:0,class:"card text-center py-8"},E={key:3,class:"mt-6 card bg-green-50 border border-green-200"},H={class:"flex items-center space-x-3"},q={class:"font-medium text-gray-900"},G={key:0,class:"text-sm text-gray-600"},J=f({__name:"StaffSelectionView",setup(K){const r=x(),n=p(()=>r.selectedStaff),g=c=>{r.setStaff(c)};return b(async()=>{r.staffMembers.length===0&&await r.fetchStaffMembers()}),(c,t)=>(a(),l("div",null,[t[9]||(t[9]=e("div",{class:"mb-6"},[e("h2",{class:"text-xl font-bold text-gray-900 mb-2"},"選擇服務人員"),e("p",{class:"text-gray-600"},"請選擇您希望為您服務的專業人員")],-1)),o(r).isLoading?(a(),l("div",_,[(a(),l(v,null,u(3,s=>e("div",{key:s,class:"card animate-pulse"},t[1]||(t[1]=[m('<div class="flex items-center space-x-4" data-v-261c8c03><div class="w-16 h-16 bg-gray-200 rounded-full" data-v-261c8c03></div><div class="flex-1" data-v-261c8c03><div class="h-4 bg-gray-200 rounded mb-2" data-v-261c8c03></div><div class="h-3 bg-gray-200 rounded w-2/3" data-v-261c8c03></div></div></div>',1)]))),64))])):o(r).error?(a(),l("div",w,[t[2]||(t[2]=e("div",{class:"w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center"},[e("svg",{class:"w-8 h-8 text-red-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])],-1)),t[3]||(t[3]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-2"},"載入失敗",-1)),e("p",k,d(o(r).error),1),e("button",{onClick:t[0]||(t[0]=s=>o(r).fetchStaffMembers()),class:"btn-primary"}," 重新載入 ")])):(a(),l("div",S,[(a(!0),l(v,null,u(o(r).staffMembers,s=>(a(),l("div",{key:s.id,onClick:O=>g(s.id),class:h(["card cursor-pointer transition-all duration-200 hover:shadow-lg",{"ring-2 ring-line-green bg-green-50":o(r).bookingForm.staff_id===s.id,"hover:shadow-md":o(r).bookingForm.staff_id!==s.id}])},[e("div",M,[e("div",z,[s.avatar_url?(a(),l("img",{key:0,src:s.avatar_url,alt:s.name,class:"w-16 h-16 rounded-full object-cover"},null,8,B)):(a(),l("div",V,[e("span",j,d(s.name.charAt(0)),1)])),o(r).bookingForm.staff_id===s.id?(a(),l("div",L,t[4]||(t[4]=[e("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]))):i("",!0)]),e("div",F,[e("h3",N,d(s.name),1),s.title?(a(),l("p",I,d(s.title),1)):i("",!0),s.description?(a(),l("p",$,d(s.description),1)):i("",!0),s.working_hours?(a(),l("div",A,t[5]||(t[5]=[e("div",{class:"flex items-center text-xs text-gray-500"},[e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})]),e("span",null,"今日 09:00 - 18:00")],-1)]))):i("",!0)]),t[6]||(t[6]=e("div",{class:"text-gray-400"},[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])],-1))])],10,C))),128)),o(r).staffMembers.length===0?(a(),l("div",D,t[7]||(t[7]=[m('<div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center" data-v-261c8c03><svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20" data-v-261c8c03><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" data-v-261c8c03></path></svg></div><h3 class="text-lg font-semibold text-gray-900 mb-2" data-v-261c8c03>暫無可用人員</h3><p class="text-gray-600" data-v-261c8c03>目前沒有可預約的服務人員</p>',3)]))):i("",!0)])),n.value?(a(),l("div",E,[e("div",H,[t[8]||(t[8]=e("div",{class:"w-8 h-8 bg-line-green rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("p",q,"已選擇："+d(n.value.name),1),n.value.title?(a(),l("p",G,d(n.value.title),1)):i("",!0)])])])):i("",!0)]))}}),R=y(J,[["__scopeId","data-v-261c8c03"]]);export{R as default};
