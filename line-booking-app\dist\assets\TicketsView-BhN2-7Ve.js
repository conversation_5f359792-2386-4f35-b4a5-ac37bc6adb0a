import{u as C}from"./booking-CCzFsPCM.js";import{d as L,u as B,r as b,g as v,h as $,c as r,a as e,t as n,F as x,k as g,p as _,f as E,i as p,o as l}from"./index-CK6tErNr.js";const F={class:"space-y-6"},N={class:"grid grid-cols-2 gap-4"},j={class:"card text-center bg-green-50 border border-green-200"},A={class:"text-2xl font-bold text-green-600 mb-1"},W={class:"card text-center bg-gray-50 border border-gray-200"},I={class:"text-2xl font-bold text-gray-600 mb-1"},U={class:"card"},q={class:"flex space-x-2"},G=["onClick"],J={key:0,class:"space-y-4"},K={key:1,class:"space-y-4"},O={class:"flex items-center space-x-4"},P={class:"w-8 h-8",fill:"currentColor",viewBox:"0 0 20 20"},Q={key:0,d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"},R={key:1,"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},X={key:2,"fill-rule":"evenodd",d:"M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z","clip-rule":"evenodd"},Y={class:"flex-1 min-w-0"},Z={class:"flex items-start justify-between"},ee={class:"flex-1"},se={class:"font-semibold text-gray-900 mb-1"},te={key:0,class:"text-sm text-gray-600 mb-2"},ae={class:"flex items-center space-x-4 text-sm"},re={class:"font-medium text-line-green"},le={key:0,class:"text-gray-500"},ne={class:"flex flex-col items-end space-y-2"},oe={key:0,class:"px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-full"},ie={key:1,class:"px-2 py-1 bg-red-100 text-red-600 text-xs font-medium rounded-full"},de={key:2,class:"px-2 py-1 bg-yellow-100 text-yellow-600 text-xs font-medium rounded-full"},ce={key:3,class:"px-2 py-1 bg-green-100 text-green-600 text-xs font-medium rounded-full"},ue={key:0,class:"mt-3 pt-3 border-t border-gray-200"},ve={class:"flex items-center text-xs text-gray-500"},xe={key:2,class:"card text-center py-12"},ge={class:"text-lg font-semibold text-gray-900 mb-2"},_e={class:"text-gray-600 mb-6"},fe=L({__name:"TicketsView",setup(pe){const o=C(),y=B(),c=b(!1),i=b("all"),w=[{value:"all",label:"全部"},{value:"available",label:"可用"},{value:"used",label:"已用"},{value:"expired",label:"過期"}],m=v(()=>o.userTickets.filter(t=>!t.is_used&&!d(t.expiry_date))),f=v(()=>o.userTickets.filter(t=>t.is_used)),h=v(()=>{switch(i.value){case"available":return m.value;case"used":return f.value;case"expired":return o.userTickets.filter(t=>!t.is_used&&d(t.expiry_date));default:return o.userTickets}}),d=t=>t?new Date(t)<new Date:!1,z=t=>{if(!t)return!1;const a=new Date(t),s=new Date,u=Math.ceil((a.getTime()-s.getTime())/(1e3*60*60*24));return u<=7&&u>0},T=t=>({discount:"bg-purple-100 text-purple-600",free_service:"bg-green-100 text-green-600",cashback:"bg-orange-100 text-orange-600"})[t]||"bg-gray-100 text-gray-600",k=t=>t.discount_type==="percentage"?`${t.discount_value}% 折扣`:t.discount_type==="fixed_amount"?`NT$ ${t.discount_value} 折抵`:"優惠券",H=t=>new Date(t).toLocaleDateString("zh-TW"),M=t=>new Date(t).toLocaleString("zh-TW"),D=()=>{switch(i.value){case"available":return"暫無可用票券";case"used":return"暫無使用記錄";case"expired":return"暫無過期票券";default:return"暫無票券"}},S=()=>{switch(i.value){case"available":return"您目前沒有可用的票券，完成預約可獲得獎勵票券";case"used":return"您還沒有使用過任何票券";case"expired":return"您沒有過期的票券";default:return"您還沒有任何票券，完成預約可獲得獎勵票券"}},V=async()=>{if(y.user){c.value=!0;try{await o.fetchUserTickets(y.user.id)}catch(t){console.error("載入票券失敗:",t)}finally{c.value=!1}}};return $(()=>{V()}),(t,a)=>(l(),r("div",F,[a[6]||(a[6]=e("div",null,[e("h2",{class:"text-xl font-bold text-gray-900 mb-2"},"我的票券"),e("p",{class:"text-gray-600"},"管理您的優惠票券")],-1)),e("div",N,[e("div",j,[e("div",A,n(m.value.length),1),a[1]||(a[1]=e("div",{class:"text-sm text-gray-600"},"可用票券",-1))]),e("div",W,[e("div",I,n(f.value.length),1),a[2]||(a[2]=e("div",{class:"text-sm text-gray-600"},"已用票券",-1))])]),e("div",U,[e("div",q,[(l(),r(x,null,g(w,s=>e("button",{key:s.value,onClick:u=>i.value=s.value,class:_(["px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200",i.value===s.value?"bg-line-green text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"])},n(s.label),11,G)),64))])]),c.value?(l(),r("div",J,[(l(),r(x,null,g(3,s=>e("div",{key:s,class:"card animate-pulse"},a[3]||(a[3]=[E('<div class="flex items-center space-x-4"><div class="w-16 h-16 bg-gray-200 rounded-lg"></div><div class="flex-1"><div class="h-4 bg-gray-200 rounded mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div>',1)]))),64))])):h.value.length>0?(l(),r("div",K,[(l(!0),r(x,null,g(h.value,s=>(l(),r("div",{key:s.id,class:_(["card transition-all duration-200",{"opacity-60":s.is_used||d(s.expiry_date),"hover:shadow-lg":!s.is_used&&!d(s.expiry_date)}])},[e("div",O,[e("div",{class:_(["w-16 h-16 rounded-lg flex items-center justify-center",T(s.ticket_type)])},[(l(),r("svg",P,[s.ticket_type==="discount"?(l(),r("path",Q)):s.ticket_type==="free_service"?(l(),r("path",R)):(l(),r("path",X))]))],2),e("div",Y,[e("div",Z,[e("div",ee,[e("h3",se,n(s.title),1),s.description?(l(),r("p",te,n(s.description),1)):p("",!0),e("div",ae,[e("span",re,n(k(s)),1),s.expiry_date?(l(),r("span",le," 到期："+n(H(s.expiry_date)),1)):p("",!0)])]),e("div",ne,[s.is_used?(l(),r("span",oe," 已使用 ")):d(s.expiry_date)?(l(),r("span",ie," 已過期 ")):z(s.expiry_date)?(l(),r("span",de," 即將到期 ")):(l(),r("span",ce," 可使用 "))])]),s.is_used&&s.used_at?(l(),r("div",ue,[e("div",ve,[a[4]||(a[4]=e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),e("span",null,"使用時間："+n(M(s.used_at)),1)])])):p("",!0)])])],2))),128))])):(l(),r("div",xe,[a[5]||(a[5]=e("div",{class:"w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center"},[e("svg",{class:"w-8 h-8 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"})])],-1)),e("h3",ge,n(D()),1),e("p",_e,n(S()),1),e("button",{onClick:a[0]||(a[0]=s=>t.$router.push("/booking")),class:"btn-primary"}," 立即預約 ")])),a[7]||(a[7]=e("div",{class:"card bg-blue-50 border border-blue-200"},[e("h4",{class:"font-medium text-gray-900 mb-3"},"票券使用說明"),e("ul",{class:"text-sm text-gray-600 space-y-1"},[e("li",null,"• 票券僅限本人使用，不可轉讓"),e("li",null,"• 請在有效期限內使用，過期無效"),e("li",null,"• 每次預約僅能使用一張票券"),e("li",null,"• 票券使用後無法退還或重複使用"),e("li",null,"• 如有疑問請聯絡客服")])],-1))]))}});export{fe as default};
