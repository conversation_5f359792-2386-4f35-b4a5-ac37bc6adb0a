import{A as N,r as o,g as d,B as u}from"./index-8Cki6B3T.js";const C=N("booking",()=>{const g=o([]),k=o([]),n=o([]),p=o([]),h=o([]),t=o({staff_id:"",service_id:"",booking_date:"",booking_time:"",selected_tickets:[],notes:""}),s=o(!1),i=o(null),B=d(()=>g.value.find(e=>e.id===t.value.staff_id)),m=d(()=>k.value.find(e=>e.id===t.value.service_id)),w=d(()=>h.value.filter(e=>t.value.selected_tickets.includes(e.id))),b=d(()=>m.value?m.value.price:0),y=d(()=>w.value.reduce((e,a)=>a.discount_type==="fixed_amount"?e+(a.discount_value||0):a.discount_type==="percentage"?e+b.value*(a.discount_value||0)/100:e,0)),S=d(()=>Math.max(0,b.value-y.value)),T=async()=>{s.value=!0,i.value=null;try{const{data:e,error:a}=await u.staff.getActive();if(a)throw console.error("Supabase error:",a),new Error(a.message||"獲取服務人員資料失敗");g.value=e||[]}catch(e){i.value=e instanceof Error?e.message:"獲取服務人員資料失敗",console.error("獲取服務人員失敗:",e)}finally{s.value=!1}},A=async()=>{s.value=!0,i.value=null;try{const{data:e,error:a}=await u.services.getActive();if(a)throw console.error("Supabase error:",a),new Error(a.message||"獲取服務項目資料失敗");k.value=e||[]}catch(e){i.value=e instanceof Error?e.message:"獲取服務項目資料失敗",console.error("獲取服務項目失敗:",e)}finally{s.value=!1}},D=async e=>{s.value=!0,i.value=null;try{const{data:a,error:r}=await u.bookings.getByUserId(e);if(r)throw r;p.value=a||[]}catch(a){i.value="獲取預約記錄失敗",console.error(a)}finally{s.value=!1}},U=async e=>{s.value=!0,i.value=null;try{const{data:a,error:r}=await u.tickets.getAvailableByUserId(e);if(r)throw r;h.value=a||[]}catch(a){i.value="獲取票券資料失敗",console.error(a)}finally{s.value=!1}},M=async(e,a)=>{s.value=!0,i.value=null;try{const r=m.value?.duration||60,{data:c,error:l}=await u.staff.getAvailableTimeSlots(e,a,r);if(l)throw console.error("Supabase error:",l),new Error(l.message||"檢查可用時間失敗");const _=(c||[]).map(f=>({time:f.time_slot,available:f.is_available,staff_id:e})),v={date:a,available:_.some(f=>f.available),time_slots:_},x=n.value.findIndex(f=>f.date===a);x>=0?n.value[x]=v:n.value.push(v)}catch(r){i.value=r instanceof Error?r.message:"檢查可用時間失敗",console.error("檢查可用時間失敗:",r);const c=[{time:"09:00",available:!0,staff_id:e},{time:"09:30",available:!0,staff_id:e},{time:"10:00",available:!0,staff_id:e},{time:"10:30",available:!1,staff_id:e},{time:"11:00",available:!0,staff_id:e},{time:"11:30",available:!0,staff_id:e},{time:"14:00",available:!0,staff_id:e},{time:"14:30",available:!0,staff_id:e},{time:"15:00",available:!0,staff_id:e},{time:"15:30",available:!1,staff_id:e},{time:"16:00",available:!0,staff_id:e},{time:"16:30",available:!0,staff_id:e}],l={date:a,available:c.some(v=>v.available),time_slots:c},_=n.value.findIndex(v=>v.date===a);_>=0?n.value[_]=l:n.value.push(l)}finally{s.value=!1}},F=async e=>{s.value=!0,i.value=null;try{const a={user_id:e,staff_id:t.value.staff_id,service_id:t.value.service_id,booking_date:t.value.booking_date,booking_time:t.value.booking_time,total_price:b.value,discount_amount:y.value,final_price:S.value,notes:t.value.notes,status:"pending"},{data:r,error:c}=await u.bookings.create(a);if(c)throw c;for(const l of t.value.selected_tickets)await u.tickets.use(l,r?.id||"");return E(),r}catch(a){throw i.value="創建預約失敗",console.error(a),a}finally{s.value=!1}},E=()=>{t.value={staff_id:"",service_id:"",booking_date:"",booking_time:"",selected_tickets:[],notes:""}};return{staffMembers:g,serviceItems:k,availableDates:n,userBookings:p,userTickets:h,bookingForm:t,isLoading:s,error:i,selectedStaff:B,selectedService:m,selectedTickets:w,totalPrice:b,discountAmount:y,finalPrice:S,fetchStaffMembers:T,fetchServiceItems:A,fetchUserBookings:D,fetchUserTickets:U,checkAvailability:M,createBooking:F,resetBookingForm:E,setStaff:e=>{t.value.staff_id=e},setService:e=>{t.value.service_id=e},setDateTime:(e,a)=>{t.value.booking_date=e,t.value.booking_time=a},toggleTicket:e=>{const a=t.value.selected_tickets.indexOf(e);a>=0?t.value.selected_tickets.splice(a,1):t.value.selected_tickets.push(e)},setNotes:e=>{t.value.notes=e}}});export{C as u};
