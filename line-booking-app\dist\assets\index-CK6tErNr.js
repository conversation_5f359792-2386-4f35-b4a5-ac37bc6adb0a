const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/BookingLayout-D-sJMg24.js","assets/booking-CCzFsPCM.js","assets/StaffSelectionView-BPEdZK_d.js","assets/StaffSelectionView-BXPX158s.css","assets/ServiceSelectionView-BXCCXftL.js","assets/ServiceSelectionView-DY9bzn39.css","assets/DateTimeSelectionView-Pa-e5TkS.js","assets/BookingConfirmView-BSc_oNxr.js","assets/BookingsView-v11bV0Bf.js","assets/TicketsView-BhN2-7Ve.js","assets/BookingSuccessView-CTwiRqd0.js"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))r(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function s(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(n){if(n.ep)return;n.ep=!0;const i=s(n);fetch(n.href,i)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Cn(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const re={},is=[],Ye=()=>{},El=()=>!1,kr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),In=e=>e.startsWith("onUpdate:"),we=Object.assign,jn=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},kl=Object.prototype.hasOwnProperty,ee=(e,t)=>kl.call(e,t),N=Array.isArray,os=e=>Tr(e)==="[object Map]",Io=e=>Tr(e)==="[object Set]",F=e=>typeof e=="function",he=e=>typeof e=="string",xt=e=>typeof e=="symbol",le=e=>e!==null&&typeof e=="object",jo=e=>(le(e)||F(e))&&F(e.then)&&F(e.catch),$o=Object.prototype.toString,Tr=e=>$o.call(e),Tl=e=>Tr(e).slice(8,-1),Lo=e=>Tr(e)==="[object Object]",$n=e=>he(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Es=Cn(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Or=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Ol=/-(\w)/g,Ne=Or(e=>e.replace(Ol,(t,s)=>s?s.toUpperCase():"")),Pl=/\B([A-Z])/g,Bt=Or(e=>e.replace(Pl,"-$1").toLowerCase()),Pr=Or(e=>e.charAt(0).toUpperCase()+e.slice(1)),Nr=Or(e=>e?`on${Pr(e)}`:""),kt=(e,t)=>!Object.is(e,t),cr=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},on=(e,t,s,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:s})},an=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let li;const xr=()=>li||(li=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ln(e){if(N(e)){const t={};for(let s=0;s<e.length;s++){const r=e[s],n=he(r)?Cl(r):Ln(r);if(n)for(const i in n)t[i]=n[i]}return t}else if(he(e)||le(e))return e}const xl=/;(?![^(]*\))/g,Al=/:([^]+)/,Rl=/\/\*[^]*?\*\//g;function Cl(e){const t={};return e.replace(Rl,"").split(xl).forEach(s=>{if(s){const r=s.split(Al);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Un(e){let t="";if(he(e))t=e;else if(N(e))for(let s=0;s<e.length;s++){const r=Un(e[s]);r&&(t+=r+" ")}else if(le(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Il="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",jl=Cn(Il);function Uo(e){return!!e||e===""}const Do=e=>!!(e&&e.__v_isRef===!0),$l=e=>he(e)?e:e==null?"":N(e)||le(e)&&(e.toString===$o||!F(e.toString))?Do(e)?$l(e.value):JSON.stringify(e,Mo,2):String(e),Mo=(e,t)=>Do(t)?Mo(e,t.value):os(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[r,n],i)=>(s[Br(r,i)+" =>"]=n,s),{})}:Io(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>Br(s))}:xt(t)?Br(t):le(t)&&!N(t)&&!Lo(t)?String(t):t,Br=(e,t="")=>{var s;return xt(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ve;class No{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ve,!t&&ve&&(this.index=(ve.scopes||(ve.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=ve;try{return ve=this,t()}finally{ve=s}}}on(){++this._on===1&&(this.prevScope=ve,ve=this)}off(){this._on>0&&--this._on===0&&(ve=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,r;for(s=0,r=this.effects.length;s<r;s++)this.effects[s].stop();for(this.effects.length=0,s=0,r=this.cleanups.length;s<r;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,r=this.scopes.length;s<r;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function Bo(e){return new No(e)}function qo(){return ve}function Ll(e,t=!1){ve&&ve.cleanups.push(e)}let oe;const qr=new WeakSet;class Fo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ve&&ve.active&&ve.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,qr.has(this)&&(qr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Wo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ci(this),Vo(this);const t=oe,s=Fe;oe=this,Fe=!0;try{return this.fn()}finally{Ko(this),oe=t,Fe=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Nn(t);this.deps=this.depsTail=void 0,ci(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?qr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ln(this)&&this.run()}get dirty(){return ln(this)}}let Ho=0,ks,Ts;function Wo(e,t=!1){if(e.flags|=8,t){e.next=Ts,Ts=e;return}e.next=ks,ks=e}function Dn(){Ho++}function Mn(){if(--Ho>0)return;if(Ts){let t=Ts;for(Ts=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;ks;){let t=ks;for(ks=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=s}}if(e)throw e}function Vo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ko(e){let t,s=e.depsTail,r=s;for(;r;){const n=r.prevDep;r.version===-1?(r===s&&(s=n),Nn(r),Ul(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=n}e.deps=t,e.depsTail=s}function ln(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(zo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function zo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Bs)||(e.globalVersion=Bs,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!ln(e))))return;e.flags|=2;const t=e.dep,s=oe,r=Fe;oe=e,Fe=!0;try{Vo(e);const n=e.fn(e._value);(t.version===0||kt(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{oe=s,Fe=r,Ko(e),e.flags&=-3}}function Nn(e,t=!1){const{dep:s,prevSub:r,nextSub:n}=e;if(r&&(r.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=r,e.nextSub=void 0),s.subs===e&&(s.subs=r,!r&&s.computed)){s.computed.flags&=-5;for(let i=s.computed.deps;i;i=i.nextDep)Nn(i,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function Ul(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Fe=!0;const Go=[];function ct(){Go.push(Fe),Fe=!1}function ut(){const e=Go.pop();Fe=e===void 0?!0:e}function ci(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=oe;oe=void 0;try{t()}finally{oe=s}}}let Bs=0;class Dl{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Bn{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!oe||!Fe||oe===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==oe)s=this.activeLink=new Dl(oe,this),oe.deps?(s.prevDep=oe.depsTail,oe.depsTail.nextDep=s,oe.depsTail=s):oe.deps=oe.depsTail=s,Jo(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const r=s.nextDep;r.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=r),s.prevDep=oe.depsTail,s.nextDep=void 0,oe.depsTail.nextDep=s,oe.depsTail=s,oe.deps===s&&(oe.deps=r)}return s}trigger(t){this.version++,Bs++,this.notify(t)}notify(t){Dn();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Mn()}}}function Jo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Jo(r)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const pr=new WeakMap,Mt=Symbol(""),cn=Symbol(""),qs=Symbol("");function ye(e,t,s){if(Fe&&oe){let r=pr.get(e);r||pr.set(e,r=new Map);let n=r.get(s);n||(r.set(s,n=new Bn),n.map=r,n.key=s),n.track()}}function ot(e,t,s,r,n,i){const o=pr.get(e);if(!o){Bs++;return}const a=l=>{l&&l.trigger()};if(Dn(),t==="clear")o.forEach(a);else{const l=N(e),u=l&&$n(s);if(l&&s==="length"){const c=Number(r);o.forEach((h,d)=>{(d==="length"||d===qs||!xt(d)&&d>=c)&&a(h)})}else switch((s!==void 0||o.has(void 0))&&a(o.get(s)),u&&a(o.get(qs)),t){case"add":l?u&&a(o.get("length")):(a(o.get(Mt)),os(e)&&a(o.get(cn)));break;case"delete":l||(a(o.get(Mt)),os(e)&&a(o.get(cn)));break;case"set":os(e)&&a(o.get(Mt));break}}Mn()}function Ml(e,t){const s=pr.get(e);return s&&s.get(t)}function Wt(e){const t=Y(e);return t===e?t:(ye(t,"iterate",qs),De(e)?t:t.map(ge))}function Ar(e){return ye(e=Y(e),"iterate",qs),e}const Nl={__proto__:null,[Symbol.iterator](){return Fr(this,Symbol.iterator,ge)},concat(...e){return Wt(this).concat(...e.map(t=>N(t)?Wt(t):t))},entries(){return Fr(this,"entries",e=>(e[1]=ge(e[1]),e))},every(e,t){return et(this,"every",e,t,void 0,arguments)},filter(e,t){return et(this,"filter",e,t,s=>s.map(ge),arguments)},find(e,t){return et(this,"find",e,t,ge,arguments)},findIndex(e,t){return et(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return et(this,"findLast",e,t,ge,arguments)},findLastIndex(e,t){return et(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return et(this,"forEach",e,t,void 0,arguments)},includes(...e){return Hr(this,"includes",e)},indexOf(...e){return Hr(this,"indexOf",e)},join(e){return Wt(this).join(e)},lastIndexOf(...e){return Hr(this,"lastIndexOf",e)},map(e,t){return et(this,"map",e,t,void 0,arguments)},pop(){return gs(this,"pop")},push(...e){return gs(this,"push",e)},reduce(e,...t){return ui(this,"reduce",e,t)},reduceRight(e,...t){return ui(this,"reduceRight",e,t)},shift(){return gs(this,"shift")},some(e,t){return et(this,"some",e,t,void 0,arguments)},splice(...e){return gs(this,"splice",e)},toReversed(){return Wt(this).toReversed()},toSorted(e){return Wt(this).toSorted(e)},toSpliced(...e){return Wt(this).toSpliced(...e)},unshift(...e){return gs(this,"unshift",e)},values(){return Fr(this,"values",ge)}};function Fr(e,t,s){const r=Ar(e),n=r[t]();return r!==e&&!De(e)&&(n._next=n.next,n.next=()=>{const i=n._next();return i.value&&(i.value=s(i.value)),i}),n}const Bl=Array.prototype;function et(e,t,s,r,n,i){const o=Ar(e),a=o!==e&&!De(e),l=o[t];if(l!==Bl[t]){const h=l.apply(e,i);return a?ge(h):h}let u=s;o!==e&&(a?u=function(h,d){return s.call(this,ge(h),d,e)}:s.length>2&&(u=function(h,d){return s.call(this,h,d,e)}));const c=l.call(o,u,r);return a&&n?n(c):c}function ui(e,t,s,r){const n=Ar(e);let i=s;return n!==e&&(De(e)?s.length>3&&(i=function(o,a,l){return s.call(this,o,a,l,e)}):i=function(o,a,l){return s.call(this,o,ge(a),l,e)}),n[t](i,...r)}function Hr(e,t,s){const r=Y(e);ye(r,"iterate",qs);const n=r[t](...s);return(n===-1||n===!1)&&Hn(s[0])?(s[0]=Y(s[0]),r[t](...s)):n}function gs(e,t,s=[]){ct(),Dn();const r=Y(e)[t].apply(e,s);return Mn(),ut(),r}const ql=Cn("__proto__,__v_isRef,__isVue"),Qo=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(xt));function Fl(e){xt(e)||(e=String(e));const t=Y(this);return ye(t,"has",e),t.hasOwnProperty(e)}class Yo{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,r){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,i=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return i;if(s==="__v_raw")return r===(n?i?Xl:ta:i?ea:Zo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const o=N(t);if(!n){let l;if(o&&(l=Nl[s]))return l;if(s==="hasOwnProperty")return Fl}const a=Reflect.get(t,s,ue(t)?t:r);return(xt(s)?Qo.has(s):ql(s))||(n||ye(t,"get",s),i)?a:ue(a)?o&&$n(s)?a:a.value:le(a)?n?ra(a):Gs(a):a}}class Xo extends Yo{constructor(t=!1){super(!1,t)}set(t,s,r,n){let i=t[s];if(!this._isShallow){const l=Ot(i);if(!De(r)&&!Ot(r)&&(i=Y(i),r=Y(r)),!N(t)&&ue(i)&&!ue(r))return l?!1:(i.value=r,!0)}const o=N(t)&&$n(s)?Number(s)<t.length:ee(t,s),a=Reflect.set(t,s,r,ue(t)?t:n);return t===Y(n)&&(o?kt(r,i)&&ot(t,"set",s,r):ot(t,"add",s,r)),a}deleteProperty(t,s){const r=ee(t,s);t[s];const n=Reflect.deleteProperty(t,s);return n&&r&&ot(t,"delete",s,void 0),n}has(t,s){const r=Reflect.has(t,s);return(!xt(s)||!Qo.has(s))&&ye(t,"has",s),r}ownKeys(t){return ye(t,"iterate",N(t)?"length":Mt),Reflect.ownKeys(t)}}class Hl extends Yo{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Wl=new Xo,Vl=new Hl,Kl=new Xo(!0);const un=e=>e,Zs=e=>Reflect.getPrototypeOf(e);function zl(e,t,s){return function(...r){const n=this.__v_raw,i=Y(n),o=os(i),a=e==="entries"||e===Symbol.iterator&&o,l=e==="keys"&&o,u=n[e](...r),c=s?un:t?gr:ge;return!t&&ye(i,"iterate",l?cn:Mt),{next(){const{value:h,done:d}=u.next();return d?{value:h,done:d}:{value:a?[c(h[0]),c(h[1])]:c(h),done:d}},[Symbol.iterator](){return this}}}}function er(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Gl(e,t){const s={get(n){const i=this.__v_raw,o=Y(i),a=Y(n);e||(kt(n,a)&&ye(o,"get",n),ye(o,"get",a));const{has:l}=Zs(o),u=t?un:e?gr:ge;if(l.call(o,n))return u(i.get(n));if(l.call(o,a))return u(i.get(a));i!==o&&i.get(n)},get size(){const n=this.__v_raw;return!e&&ye(Y(n),"iterate",Mt),Reflect.get(n,"size",n)},has(n){const i=this.__v_raw,o=Y(i),a=Y(n);return e||(kt(n,a)&&ye(o,"has",n),ye(o,"has",a)),n===a?i.has(n):i.has(n)||i.has(a)},forEach(n,i){const o=this,a=o.__v_raw,l=Y(a),u=t?un:e?gr:ge;return!e&&ye(l,"iterate",Mt),a.forEach((c,h)=>n.call(i,u(c),u(h),o))}};return we(s,e?{add:er("add"),set:er("set"),delete:er("delete"),clear:er("clear")}:{add(n){!t&&!De(n)&&!Ot(n)&&(n=Y(n));const i=Y(this);return Zs(i).has.call(i,n)||(i.add(n),ot(i,"add",n,n)),this},set(n,i){!t&&!De(i)&&!Ot(i)&&(i=Y(i));const o=Y(this),{has:a,get:l}=Zs(o);let u=a.call(o,n);u||(n=Y(n),u=a.call(o,n));const c=l.call(o,n);return o.set(n,i),u?kt(i,c)&&ot(o,"set",n,i):ot(o,"add",n,i),this},delete(n){const i=Y(this),{has:o,get:a}=Zs(i);let l=o.call(i,n);l||(n=Y(n),l=o.call(i,n)),a&&a.call(i,n);const u=i.delete(n);return l&&ot(i,"delete",n,void 0),u},clear(){const n=Y(this),i=n.size!==0,o=n.clear();return i&&ot(n,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=zl(n,e,t)}),s}function qn(e,t){const s=Gl(e,t);return(r,n,i)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?r:Reflect.get(ee(s,n)&&n in r?s:r,n,i)}const Jl={get:qn(!1,!1)},Ql={get:qn(!1,!0)},Yl={get:qn(!0,!1)};const Zo=new WeakMap,ea=new WeakMap,ta=new WeakMap,Xl=new WeakMap;function Zl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ec(e){return e.__v_skip||!Object.isExtensible(e)?0:Zl(Tl(e))}function Gs(e){return Ot(e)?e:Fn(e,!1,Wl,Jl,Zo)}function sa(e){return Fn(e,!1,Kl,Ql,ea)}function ra(e){return Fn(e,!0,Vl,Yl,ta)}function Fn(e,t,s,r,n){if(!le(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=ec(e);if(i===0)return e;const o=n.get(e);if(o)return o;const a=new Proxy(e,i===2?r:s);return n.set(e,a),a}function Tt(e){return Ot(e)?Tt(e.__v_raw):!!(e&&e.__v_isReactive)}function Ot(e){return!!(e&&e.__v_isReadonly)}function De(e){return!!(e&&e.__v_isShallow)}function Hn(e){return e?!!e.__v_raw:!1}function Y(e){const t=e&&e.__v_raw;return t?Y(t):e}function Wn(e){return!ee(e,"__v_skip")&&Object.isExtensible(e)&&on(e,"__v_skip",!0),e}const ge=e=>le(e)?Gs(e):e,gr=e=>le(e)?ra(e):e;function ue(e){return e?e.__v_isRef===!0:!1}function as(e){return na(e,!1)}function tc(e){return na(e,!0)}function na(e,t){return ue(e)?e:new sc(e,t)}class sc{constructor(t,s){this.dep=new Bn,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Y(t),this._value=s?t:ge(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,r=this.__v_isShallow||De(t)||Ot(t);t=r?t:Y(t),kt(t,s)&&(this._rawValue=t,this._value=r?t:ge(t),this.dep.trigger())}}function lt(e){return ue(e)?e.value:e}const rc={get:(e,t,s)=>t==="__v_raw"?e:lt(Reflect.get(e,t,s)),set:(e,t,s,r)=>{const n=e[t];return ue(n)&&!ue(s)?(n.value=s,!0):Reflect.set(e,t,s,r)}};function ia(e){return Tt(e)?e:new Proxy(e,rc)}function nc(e){const t=N(e)?new Array(e.length):{};for(const s in e)t[s]=oc(e,s);return t}class ic{constructor(t,s,r){this._object=t,this._key=s,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Ml(Y(this._object),this._key)}}function oc(e,t,s){const r=e[t];return ue(r)?r:new ic(e,t,s)}class ac{constructor(t,s,r){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Bn(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Bs-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&oe!==this)return Wo(this,!0),!0}get value(){const t=this.dep.track();return zo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function lc(e,t,s=!1){let r,n;return F(e)?r=e:(r=e.get,n=e.set),new ac(r,n,s)}const tr={},_r=new WeakMap;let Lt;function cc(e,t=!1,s=Lt){if(s){let r=_r.get(s);r||_r.set(s,r=[]),r.push(e)}}function uc(e,t,s=re){const{immediate:r,deep:n,once:i,scheduler:o,augmentJob:a,call:l}=s,u=T=>n?T:De(T)||n===!1||n===0?at(T,1):at(T);let c,h,d,f,m=!1,v=!1;if(ue(e)?(h=()=>e.value,m=De(e)):Tt(e)?(h=()=>u(e),m=!0):N(e)?(v=!0,m=e.some(T=>Tt(T)||De(T)),h=()=>e.map(T=>{if(ue(T))return T.value;if(Tt(T))return u(T);if(F(T))return l?l(T,2):T()})):F(e)?t?h=l?()=>l(e,2):e:h=()=>{if(d){ct();try{d()}finally{ut()}}const T=Lt;Lt=c;try{return l?l(e,3,[f]):e(f)}finally{Lt=T}}:h=Ye,t&&n){const T=h,L=n===!0?1/0:n;h=()=>at(T(),L)}const k=qo(),A=()=>{c.stop(),k&&k.active&&jn(k.effects,c)};if(i&&t){const T=t;t=(...L)=>{T(...L),A()}}let R=v?new Array(e.length).fill(tr):tr;const S=T=>{if(!(!(c.flags&1)||!c.dirty&&!T))if(t){const L=c.run();if(n||m||(v?L.some((V,q)=>kt(V,R[q])):kt(L,R))){d&&d();const V=Lt;Lt=c;try{const q=[L,R===tr?void 0:v&&R[0]===tr?[]:R,f];R=L,l?l(t,3,q):t(...q)}finally{Lt=V}}}else c.run()};return a&&a(S),c=new Fo(h),c.scheduler=o?()=>o(S,!1):S,f=T=>cc(T,!1,c),d=c.onStop=()=>{const T=_r.get(c);if(T){if(l)l(T,4);else for(const L of T)L();_r.delete(c)}},t?r?S(!0):R=c.run():o?o(S.bind(null,!0),!0):c.run(),A.pause=c.pause.bind(c),A.resume=c.resume.bind(c),A.stop=A,A}function at(e,t=1/0,s){if(t<=0||!le(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,ue(e))at(e.value,t,s);else if(N(e))for(let r=0;r<e.length;r++)at(e[r],t,s);else if(Io(e)||os(e))e.forEach(r=>{at(r,t,s)});else if(Lo(e)){for(const r in e)at(e[r],t,s);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&at(e[r],t,s)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Js(e,t,s,r){try{return r?e(...r):e()}catch(n){Rr(n,t,s)}}function Xe(e,t,s,r){if(F(e)){const n=Js(e,t,s,r);return n&&jo(n)&&n.catch(i=>{Rr(i,t,s)}),n}if(N(e)){const n=[];for(let i=0;i<e.length;i++)n.push(Xe(e[i],t,s,r));return n}}function Rr(e,t,s,r=!0){const n=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||re;if(t){let a=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${s}`;for(;a;){const c=a.ec;if(c){for(let h=0;h<c.length;h++)if(c[h](e,l,u)===!1)return}a=a.parent}if(i){ct(),Js(i,null,10,[e,l,u]),ut();return}}hc(e,s,n,r,o)}function hc(e,t,s,r=!0,n=!1){if(n)throw e;console.error(e)}const Ee=[];let Je=-1;const ls=[];let yt=null,ts=0;const oa=Promise.resolve();let mr=null;function Vn(e){const t=mr||oa;return e?t.then(this?e.bind(this):e):t}function fc(e){let t=Je+1,s=Ee.length;for(;t<s;){const r=t+s>>>1,n=Ee[r],i=Fs(n);i<e||i===e&&n.flags&2?t=r+1:s=r}return t}function Kn(e){if(!(e.flags&1)){const t=Fs(e),s=Ee[Ee.length-1];!s||!(e.flags&2)&&t>=Fs(s)?Ee.push(e):Ee.splice(fc(t),0,e),e.flags|=1,aa()}}function aa(){mr||(mr=oa.then(ca))}function dc(e){N(e)?ls.push(...e):yt&&e.id===-1?yt.splice(ts+1,0,e):e.flags&1||(ls.push(e),e.flags|=1),aa()}function hi(e,t,s=Je+1){for(;s<Ee.length;s++){const r=Ee[s];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Ee.splice(s,1),s--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function la(e){if(ls.length){const t=[...new Set(ls)].sort((s,r)=>Fs(s)-Fs(r));if(ls.length=0,yt){yt.push(...t);return}for(yt=t,ts=0;ts<yt.length;ts++){const s=yt[ts];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}yt=null,ts=0}}const Fs=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ca(e){try{for(Je=0;Je<Ee.length;Je++){const t=Ee[Je];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Js(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Je<Ee.length;Je++){const t=Ee[Je];t&&(t.flags&=-2)}Je=-1,Ee.length=0,la(),mr=null,(Ee.length||ls.length)&&ca()}}let Re=null,ua=null;function vr(e){const t=Re;return Re=e,ua=e&&e.type.__scopeId||null,t}function pc(e,t=Re,s){if(!t||e._n)return e;const r=(...n)=>{r._d&&bi(-1);const i=vr(t);let o;try{o=e(...n)}finally{vr(i),r._d&&bi(1)}return o};return r._n=!0,r._c=!0,r._d=!0,r}function mp(e,t){if(Re===null)return e;const s=$r(Re),r=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[i,o,a,l=re]=t[n];i&&(F(i)&&(i={mounted:i,updated:i}),i.deep&&at(o),r.push({dir:i,instance:s,value:o,oldValue:void 0,arg:a,modifiers:l}))}return e}function Ct(e,t,s,r){const n=e.dirs,i=t&&t.dirs;for(let o=0;o<n.length;o++){const a=n[o];i&&(a.oldValue=i[o].value);let l=a.dir[r];l&&(ct(),Xe(l,s,8,[e.el,a,e,t]),ut())}}const gc=Symbol("_vte"),_c=e=>e.__isTeleport;function zn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,zn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Gn(e,t){return F(e)?we({name:e.name},t,{setup:e}):e}function ha(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Os(e,t,s,r,n=!1){if(N(e)){e.forEach((m,v)=>Os(m,t&&(N(t)?t[v]:t),s,r,n));return}if(Ps(r)&&!n){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&Os(e,t,s,r.component.subTree);return}const i=r.shapeFlag&4?$r(r.component):r.el,o=n?null:i,{i:a,r:l}=e,u=t&&t.r,c=a.refs===re?a.refs={}:a.refs,h=a.setupState,d=Y(h),f=h===re?()=>!1:m=>ee(d,m);if(u!=null&&u!==l&&(he(u)?(c[u]=null,f(u)&&(h[u]=null)):ue(u)&&(u.value=null)),F(l))Js(l,a,12,[o,c]);else{const m=he(l),v=ue(l);if(m||v){const k=()=>{if(e.f){const A=m?f(l)?h[l]:c[l]:l.value;n?N(A)&&jn(A,i):N(A)?A.includes(i)||A.push(i):m?(c[l]=[i],f(l)&&(h[l]=c[l])):(l.value=[i],e.k&&(c[e.k]=l.value))}else m?(c[l]=o,f(l)&&(h[l]=o)):v&&(l.value=o,e.k&&(c[e.k]=o))};o?(k.id=-1,je(k,s)):k()}}}xr().requestIdleCallback;xr().cancelIdleCallback;const Ps=e=>!!e.type.__asyncLoader,fa=e=>e.type.__isKeepAlive;function mc(e,t){da(e,"a",t)}function vc(e,t){da(e,"da",t)}function da(e,t,s=_e){const r=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Cr(t,r,s),s){let n=s.parent;for(;n&&n.parent;)fa(n.parent.vnode)&&yc(r,t,s,n),n=n.parent}}function yc(e,t,s,r){const n=Cr(t,e,r,!0);ga(()=>{jn(r[t],n)},s)}function Cr(e,t,s=_e,r=!1){if(s){const n=s[e]||(s[e]=[]),i=t.__weh||(t.__weh=(...o)=>{ct();const a=Qs(s),l=Xe(t,s,e,o);return a(),ut(),l});return r?n.unshift(i):n.push(i),i}}const ht=e=>(t,s=_e)=>{(!Ws||e==="sp")&&Cr(e,(...r)=>t(...r),s)},wc=ht("bm"),pa=ht("m"),bc=ht("bu"),Sc=ht("u"),Ec=ht("bum"),ga=ht("um"),kc=ht("sp"),Tc=ht("rtg"),Oc=ht("rtc");function Pc(e,t=_e){Cr("ec",e,t)}const xc="components";function vp(e,t){return Rc(xc,e,!0,t)||e}const Ac=Symbol.for("v-ndc");function Rc(e,t,s=!0,r=!1){const n=Re||_e;if(n){const i=n.type;{const a=wu(i,!1);if(a&&(a===t||a===Ne(t)||a===Pr(Ne(t))))return i}const o=fi(n[e]||i[e],t)||fi(n.appContext[e],t);return!o&&r?i:o}}function fi(e,t){return e&&(e[t]||e[Ne(t)]||e[Pr(Ne(t))])}function yp(e,t,s,r){let n;const i=s,o=N(e);if(o||he(e)){const a=o&&Tt(e);let l=!1,u=!1;a&&(l=!De(e),u=Ot(e),e=Ar(e)),n=new Array(e.length);for(let c=0,h=e.length;c<h;c++)n[c]=t(l?u?gr(ge(e[c])):ge(e[c]):e[c],c,void 0,i)}else if(typeof e=="number"){n=new Array(e);for(let a=0;a<e;a++)n[a]=t(a+1,a,void 0,i)}else if(le(e))if(e[Symbol.iterator])n=Array.from(e,(a,l)=>t(a,l,void 0,i));else{const a=Object.keys(e);n=new Array(a.length);for(let l=0,u=a.length;l<u;l++){const c=a[l];n[l]=t(e[c],c,l,i)}}else n=[];return n}const hn=e=>e?La(e)?$r(e):hn(e.parent):null,xs=we(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>hn(e.parent),$root:e=>hn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ma(e),$forceUpdate:e=>e.f||(e.f=()=>{Kn(e.update)}),$nextTick:e=>e.n||(e.n=Vn.bind(e.proxy)),$watch:e=>Xc.bind(e)}),Wr=(e,t)=>e!==re&&!e.__isScriptSetup&&ee(e,t),Cc={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:r,data:n,props:i,accessCache:o,type:a,appContext:l}=e;let u;if(t[0]!=="$"){const f=o[t];if(f!==void 0)switch(f){case 1:return r[t];case 2:return n[t];case 4:return s[t];case 3:return i[t]}else{if(Wr(r,t))return o[t]=1,r[t];if(n!==re&&ee(n,t))return o[t]=2,n[t];if((u=e.propsOptions[0])&&ee(u,t))return o[t]=3,i[t];if(s!==re&&ee(s,t))return o[t]=4,s[t];fn&&(o[t]=0)}}const c=xs[t];let h,d;if(c)return t==="$attrs"&&ye(e.attrs,"get",""),c(e);if((h=a.__cssModules)&&(h=h[t]))return h;if(s!==re&&ee(s,t))return o[t]=4,s[t];if(d=l.config.globalProperties,ee(d,t))return d[t]},set({_:e},t,s){const{data:r,setupState:n,ctx:i}=e;return Wr(n,t)?(n[t]=s,!0):r!==re&&ee(r,t)?(r[t]=s,!0):ee(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:r,appContext:n,propsOptions:i}},o){let a;return!!s[o]||e!==re&&ee(e,o)||Wr(t,o)||(a=i[0])&&ee(a,o)||ee(r,o)||ee(xs,o)||ee(n.config.globalProperties,o)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:ee(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function di(e){return N(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let fn=!0;function Ic(e){const t=ma(e),s=e.proxy,r=e.ctx;fn=!1,t.beforeCreate&&pi(t.beforeCreate,e,"bc");const{data:n,computed:i,methods:o,watch:a,provide:l,inject:u,created:c,beforeMount:h,mounted:d,beforeUpdate:f,updated:m,activated:v,deactivated:k,beforeDestroy:A,beforeUnmount:R,destroyed:S,unmounted:T,render:L,renderTracked:V,renderTriggered:q,errorCaptured:z,serverPrefetch:G,expose:ce,inheritAttrs:be,components:Ce,directives:Te,filters:Rt}=t;if(u&&jc(u,r,null),o)for(const W in o){const X=o[W];F(X)&&(r[W]=X.bind(s))}if(n){const W=n.call(s,s);le(W)&&(e.data=Gs(W))}if(fn=!0,i)for(const W in i){const X=i[W],Ze=F(X)?X.bind(s,s):F(X.get)?X.get.bind(s,s):Ye,dt=!F(X)&&F(X.set)?X.set.bind(s):Ye,We=$e({get:Ze,set:dt});Object.defineProperty(r,W,{enumerable:!0,configurable:!0,get:()=>We.value,set:Oe=>We.value=Oe})}if(a)for(const W in a)_a(a[W],r,s,W);if(l){const W=F(l)?l.call(s):l;Reflect.ownKeys(W).forEach(X=>{ur(X,W[X])})}c&&pi(c,e,"c");function ae(W,X){N(X)?X.forEach(Ze=>W(Ze.bind(s))):X&&W(X.bind(s))}if(ae(wc,h),ae(pa,d),ae(bc,f),ae(Sc,m),ae(mc,v),ae(vc,k),ae(Pc,z),ae(Oc,V),ae(Tc,q),ae(Ec,R),ae(ga,T),ae(kc,G),N(ce))if(ce.length){const W=e.exposed||(e.exposed={});ce.forEach(X=>{Object.defineProperty(W,X,{get:()=>s[X],set:Ze=>s[X]=Ze})})}else e.exposed||(e.exposed={});L&&e.render===Ye&&(e.render=L),be!=null&&(e.inheritAttrs=be),Ce&&(e.components=Ce),Te&&(e.directives=Te),G&&ha(e)}function jc(e,t,s=Ye){N(e)&&(e=dn(e));for(const r in e){const n=e[r];let i;le(n)?"default"in n?i=Me(n.from||r,n.default,!0):i=Me(n.from||r):i=Me(n),ue(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[r]=i}}function pi(e,t,s){Xe(N(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,s)}function _a(e,t,s,r){let n=r.includes(".")?Ra(s,r):()=>s[r];if(he(e)){const i=t[e];F(i)&&As(n,i)}else if(F(e))As(n,e.bind(s));else if(le(e))if(N(e))e.forEach(i=>_a(i,t,s,r));else{const i=F(e.handler)?e.handler.bind(s):t[e.handler];F(i)&&As(n,i,e)}}function ma(e){const t=e.type,{mixins:s,extends:r}=t,{mixins:n,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,a=i.get(t);let l;return a?l=a:!n.length&&!s&&!r?l=t:(l={},n.length&&n.forEach(u=>yr(l,u,o,!0)),yr(l,t,o)),le(t)&&i.set(t,l),l}function yr(e,t,s,r=!1){const{mixins:n,extends:i}=t;i&&yr(e,i,s,!0),n&&n.forEach(o=>yr(e,o,s,!0));for(const o in t)if(!(r&&o==="expose")){const a=$c[o]||s&&s[o];e[o]=a?a(e[o],t[o]):t[o]}return e}const $c={data:gi,props:_i,emits:_i,methods:bs,computed:bs,beforeCreate:Se,created:Se,beforeMount:Se,mounted:Se,beforeUpdate:Se,updated:Se,beforeDestroy:Se,beforeUnmount:Se,destroyed:Se,unmounted:Se,activated:Se,deactivated:Se,errorCaptured:Se,serverPrefetch:Se,components:bs,directives:bs,watch:Uc,provide:gi,inject:Lc};function gi(e,t){return t?e?function(){return we(F(e)?e.call(this,this):e,F(t)?t.call(this,this):t)}:t:e}function Lc(e,t){return bs(dn(e),dn(t))}function dn(e){if(N(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Se(e,t){return e?[...new Set([].concat(e,t))]:t}function bs(e,t){return e?we(Object.create(null),e,t):t}function _i(e,t){return e?N(e)&&N(t)?[...new Set([...e,...t])]:we(Object.create(null),di(e),di(t??{})):t}function Uc(e,t){if(!e)return t;if(!t)return e;const s=we(Object.create(null),e);for(const r in t)s[r]=Se(e[r],t[r]);return s}function va(){return{app:null,config:{isNativeTag:El,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Dc=0;function Mc(e,t){return function(r,n=null){F(r)||(r=we({},r)),n!=null&&!le(n)&&(n=null);const i=va(),o=new WeakSet,a=[];let l=!1;const u=i.app={_uid:Dc++,_component:r,_props:n,_container:null,_context:i,_instance:null,version:Su,get config(){return i.config},set config(c){},use(c,...h){return o.has(c)||(c&&F(c.install)?(o.add(c),c.install(u,...h)):F(c)&&(o.add(c),c(u,...h))),u},mixin(c){return i.mixins.includes(c)||i.mixins.push(c),u},component(c,h){return h?(i.components[c]=h,u):i.components[c]},directive(c,h){return h?(i.directives[c]=h,u):i.directives[c]},mount(c,h,d){if(!l){const f=u._ceVNode||ke(r,n);return f.appContext=i,d===!0?d="svg":d===!1&&(d=void 0),e(f,c,d),l=!0,u._container=c,c.__vue_app__=u,$r(f.component)}},onUnmount(c){a.push(c)},unmount(){l&&(Xe(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,h){return i.provides[c]=h,u},runWithContext(c){const h=Nt;Nt=u;try{return c()}finally{Nt=h}}};return u}}let Nt=null;function ur(e,t){if(_e){let s=_e.provides;const r=_e.parent&&_e.parent.provides;r===s&&(s=_e.provides=Object.create(r)),s[e]=t}}function Me(e,t,s=!1){const r=_e||Re;if(r||Nt){let n=Nt?Nt._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&F(t)?t.call(r&&r.proxy):t}}function Nc(){return!!(_e||Re||Nt)}const ya={},wa=()=>Object.create(ya),ba=e=>Object.getPrototypeOf(e)===ya;function Bc(e,t,s,r=!1){const n={},i=wa();e.propsDefaults=Object.create(null),Sa(e,t,n,i);for(const o in e.propsOptions[0])o in n||(n[o]=void 0);s?e.props=r?n:sa(n):e.type.props?e.props=n:e.props=i,e.attrs=i}function qc(e,t,s,r){const{props:n,attrs:i,vnode:{patchFlag:o}}=e,a=Y(n),[l]=e.propsOptions;let u=!1;if((r||o>0)&&!(o&16)){if(o&8){const c=e.vnode.dynamicProps;for(let h=0;h<c.length;h++){let d=c[h];if(Ir(e.emitsOptions,d))continue;const f=t[d];if(l)if(ee(i,d))f!==i[d]&&(i[d]=f,u=!0);else{const m=Ne(d);n[m]=pn(l,a,m,f,e,!1)}else f!==i[d]&&(i[d]=f,u=!0)}}}else{Sa(e,t,n,i)&&(u=!0);let c;for(const h in a)(!t||!ee(t,h)&&((c=Bt(h))===h||!ee(t,c)))&&(l?s&&(s[h]!==void 0||s[c]!==void 0)&&(n[h]=pn(l,a,h,void 0,e,!0)):delete n[h]);if(i!==a)for(const h in i)(!t||!ee(t,h))&&(delete i[h],u=!0)}u&&ot(e.attrs,"set","")}function Sa(e,t,s,r){const[n,i]=e.propsOptions;let o=!1,a;if(t)for(let l in t){if(Es(l))continue;const u=t[l];let c;n&&ee(n,c=Ne(l))?!i||!i.includes(c)?s[c]=u:(a||(a={}))[c]=u:Ir(e.emitsOptions,l)||(!(l in r)||u!==r[l])&&(r[l]=u,o=!0)}if(i){const l=Y(s),u=a||re;for(let c=0;c<i.length;c++){const h=i[c];s[h]=pn(n,l,h,u[h],e,!ee(u,h))}}return o}function pn(e,t,s,r,n,i){const o=e[s];if(o!=null){const a=ee(o,"default");if(a&&r===void 0){const l=o.default;if(o.type!==Function&&!o.skipFactory&&F(l)){const{propsDefaults:u}=n;if(s in u)r=u[s];else{const c=Qs(n);r=u[s]=l.call(null,t),c()}}else r=l;n.ce&&n.ce._setProp(s,r)}o[0]&&(i&&!a?r=!1:o[1]&&(r===""||r===Bt(s))&&(r=!0))}return r}const Fc=new WeakMap;function Ea(e,t,s=!1){const r=s?Fc:t.propsCache,n=r.get(e);if(n)return n;const i=e.props,o={},a=[];let l=!1;if(!F(e)){const c=h=>{l=!0;const[d,f]=Ea(h,t,!0);we(o,d),f&&a.push(...f)};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!i&&!l)return le(e)&&r.set(e,is),is;if(N(i))for(let c=0;c<i.length;c++){const h=Ne(i[c]);mi(h)&&(o[h]=re)}else if(i)for(const c in i){const h=Ne(c);if(mi(h)){const d=i[c],f=o[h]=N(d)||F(d)?{type:d}:we({},d),m=f.type;let v=!1,k=!0;if(N(m))for(let A=0;A<m.length;++A){const R=m[A],S=F(R)&&R.name;if(S==="Boolean"){v=!0;break}else S==="String"&&(k=!1)}else v=F(m)&&m.name==="Boolean";f[0]=v,f[1]=k,(v||ee(f,"default"))&&a.push(h)}}const u=[o,a];return le(e)&&r.set(e,u),u}function mi(e){return e[0]!=="$"&&!Es(e)}const Jn=e=>e[0]==="_"||e==="$stable",Qn=e=>N(e)?e.map(Qe):[Qe(e)],Hc=(e,t,s)=>{if(t._n)return t;const r=pc((...n)=>Qn(t(...n)),s);return r._c=!1,r},ka=(e,t,s)=>{const r=e._ctx;for(const n in e){if(Jn(n))continue;const i=e[n];if(F(i))t[n]=Hc(n,i,r);else if(i!=null){const o=Qn(i);t[n]=()=>o}}},Ta=(e,t)=>{const s=Qn(t);e.slots.default=()=>s},Oa=(e,t,s)=>{for(const r in t)(s||!Jn(r))&&(e[r]=t[r])},Wc=(e,t,s)=>{const r=e.slots=wa();if(e.vnode.shapeFlag&32){const n=t.__;n&&on(r,"__",n,!0);const i=t._;i?(Oa(r,t,s),s&&on(r,"_",i,!0)):ka(t,r)}else t&&Ta(e,t)},Vc=(e,t,s)=>{const{vnode:r,slots:n}=e;let i=!0,o=re;if(r.shapeFlag&32){const a=t._;a?s&&a===1?i=!1:Oa(n,t,s):(i=!t.$stable,ka(t,n)),o=t}else t&&(Ta(e,t),o={default:1});if(i)for(const a in n)!Jn(a)&&o[a]==null&&delete n[a]},je=iu;function Kc(e){return zc(e)}function zc(e,t){const s=xr();s.__VUE__=!0;const{insert:r,remove:n,patchProp:i,createElement:o,createText:a,createComment:l,setText:u,setElementText:c,parentNode:h,nextSibling:d,setScopeId:f=Ye,insertStaticContent:m}=e,v=(p,g,_,y=null,E=null,b=null,C=void 0,x=null,P=!!g.dynamicChildren)=>{if(p===g)return;p&&!_s(p,g)&&(y=w(p),Oe(p,E,b,!0),p=null),g.patchFlag===-2&&(P=!1,g.dynamicChildren=null);const{type:O,ref:M,shapeFlag:j}=g;switch(O){case jr:k(p,g,_,y);break;case Pt:A(p,g,_,y);break;case hr:p==null&&R(g,_,y,C);break;case it:Ce(p,g,_,y,E,b,C,x,P);break;default:j&1?L(p,g,_,y,E,b,C,x,P):j&6?Te(p,g,_,y,E,b,C,x,P):(j&64||j&128)&&O.process(p,g,_,y,E,b,C,x,P,U)}M!=null&&E?Os(M,p&&p.ref,b,g||p,!g):M==null&&p&&p.ref!=null&&Os(p.ref,null,b,p,!0)},k=(p,g,_,y)=>{if(p==null)r(g.el=a(g.children),_,y);else{const E=g.el=p.el;g.children!==p.children&&u(E,g.children)}},A=(p,g,_,y)=>{p==null?r(g.el=l(g.children||""),_,y):g.el=p.el},R=(p,g,_,y)=>{[p.el,p.anchor]=m(p.children,g,_,y,p.el,p.anchor)},S=({el:p,anchor:g},_,y)=>{let E;for(;p&&p!==g;)E=d(p),r(p,_,y),p=E;r(g,_,y)},T=({el:p,anchor:g})=>{let _;for(;p&&p!==g;)_=d(p),n(p),p=_;n(g)},L=(p,g,_,y,E,b,C,x,P)=>{g.type==="svg"?C="svg":g.type==="math"&&(C="mathml"),p==null?V(g,_,y,E,b,C,x,P):G(p,g,E,b,C,x,P)},V=(p,g,_,y,E,b,C,x)=>{let P,O;const{props:M,shapeFlag:j,transition:D,dirs:B}=p;if(P=p.el=o(p.type,b,M&&M.is,M),j&8?c(P,p.children):j&16&&z(p.children,P,null,y,E,Vr(p,b),C,x),B&&Ct(p,null,y,"created"),q(P,p,p.scopeId,C,y),M){for(const ne in M)ne!=="value"&&!Es(ne)&&i(P,ne,null,M[ne],b,y);"value"in M&&i(P,"value",null,M.value,b),(O=M.onVnodeBeforeMount)&&Ge(O,y,p)}B&&Ct(p,null,y,"beforeMount");const J=Gc(E,D);J&&D.beforeEnter(P),r(P,g,_),((O=M&&M.onVnodeMounted)||J||B)&&je(()=>{O&&Ge(O,y,p),J&&D.enter(P),B&&Ct(p,null,y,"mounted")},E)},q=(p,g,_,y,E)=>{if(_&&f(p,_),y)for(let b=0;b<y.length;b++)f(p,y[b]);if(E){let b=E.subTree;if(g===b||Ia(b.type)&&(b.ssContent===g||b.ssFallback===g)){const C=E.vnode;q(p,C,C.scopeId,C.slotScopeIds,E.parent)}}},z=(p,g,_,y,E,b,C,x,P=0)=>{for(let O=P;O<p.length;O++){const M=p[O]=x?wt(p[O]):Qe(p[O]);v(null,M,g,_,y,E,b,C,x)}},G=(p,g,_,y,E,b,C)=>{const x=g.el=p.el;let{patchFlag:P,dynamicChildren:O,dirs:M}=g;P|=p.patchFlag&16;const j=p.props||re,D=g.props||re;let B;if(_&&It(_,!1),(B=D.onVnodeBeforeUpdate)&&Ge(B,_,g,p),M&&Ct(g,p,_,"beforeUpdate"),_&&It(_,!0),(j.innerHTML&&D.innerHTML==null||j.textContent&&D.textContent==null)&&c(x,""),O?ce(p.dynamicChildren,O,x,_,y,Vr(g,E),b):C||X(p,g,x,null,_,y,Vr(g,E),b,!1),P>0){if(P&16)be(x,j,D,_,E);else if(P&2&&j.class!==D.class&&i(x,"class",null,D.class,E),P&4&&i(x,"style",j.style,D.style,E),P&8){const J=g.dynamicProps;for(let ne=0;ne<J.length;ne++){const te=J[ne],Pe=j[te],xe=D[te];(xe!==Pe||te==="value")&&i(x,te,Pe,xe,E,_)}}P&1&&p.children!==g.children&&c(x,g.children)}else!C&&O==null&&be(x,j,D,_,E);((B=D.onVnodeUpdated)||M)&&je(()=>{B&&Ge(B,_,g,p),M&&Ct(g,p,_,"updated")},y)},ce=(p,g,_,y,E,b,C)=>{for(let x=0;x<g.length;x++){const P=p[x],O=g[x],M=P.el&&(P.type===it||!_s(P,O)||P.shapeFlag&198)?h(P.el):_;v(P,O,M,null,y,E,b,C,!0)}},be=(p,g,_,y,E)=>{if(g!==_){if(g!==re)for(const b in g)!Es(b)&&!(b in _)&&i(p,b,g[b],null,E,y);for(const b in _){if(Es(b))continue;const C=_[b],x=g[b];C!==x&&b!=="value"&&i(p,b,x,C,E,y)}"value"in _&&i(p,"value",g.value,_.value,E)}},Ce=(p,g,_,y,E,b,C,x,P)=>{const O=g.el=p?p.el:a(""),M=g.anchor=p?p.anchor:a("");let{patchFlag:j,dynamicChildren:D,slotScopeIds:B}=g;B&&(x=x?x.concat(B):B),p==null?(r(O,_,y),r(M,_,y),z(g.children||[],_,M,E,b,C,x,P)):j>0&&j&64&&D&&p.dynamicChildren?(ce(p.dynamicChildren,D,_,E,b,C,x),(g.key!=null||E&&g===E.subTree)&&Pa(p,g,!0)):X(p,g,_,M,E,b,C,x,P)},Te=(p,g,_,y,E,b,C,x,P)=>{g.slotScopeIds=x,p==null?g.shapeFlag&512?E.ctx.activate(g,_,y,C,P):Rt(g,_,y,E,b,C,P):ft(p,g,P)},Rt=(p,g,_,y,E,b,C)=>{const x=p.component=gu(p,y,E);if(fa(p)&&(x.ctx.renderer=U),_u(x,!1,C),x.asyncDep){if(E&&E.registerDep(x,ae,C),!p.el){const P=x.subTree=ke(Pt);A(null,P,g,_)}}else ae(x,p,g,_,E,b,C)},ft=(p,g,_)=>{const y=g.component=p.component;if(ru(p,g,_))if(y.asyncDep&&!y.asyncResolved){W(y,g,_);return}else y.next=g,y.update();else g.el=p.el,y.vnode=g},ae=(p,g,_,y,E,b,C)=>{const x=()=>{if(p.isMounted){let{next:j,bu:D,u:B,parent:J,vnode:ne}=p;{const Ke=xa(p);if(Ke){j&&(j.el=ne.el,W(p,j,C)),Ke.asyncDep.then(()=>{p.isUnmounted||x()});return}}let te=j,Pe;It(p,!1),j?(j.el=ne.el,W(p,j,C)):j=ne,D&&cr(D),(Pe=j.props&&j.props.onVnodeBeforeUpdate)&&Ge(Pe,J,j,ne),It(p,!0);const xe=yi(p),Ve=p.subTree;p.subTree=xe,v(Ve,xe,h(Ve.el),w(Ve),p,E,b),j.el=xe.el,te===null&&nu(p,xe.el),B&&je(B,E),(Pe=j.props&&j.props.onVnodeUpdated)&&je(()=>Ge(Pe,J,j,ne),E)}else{let j;const{el:D,props:B}=g,{bm:J,m:ne,parent:te,root:Pe,type:xe}=p,Ve=Ps(g);It(p,!1),J&&cr(J),!Ve&&(j=B&&B.onVnodeBeforeMount)&&Ge(j,te,g),It(p,!0);{Pe.ce&&Pe.ce._def.shadowRoot!==!1&&Pe.ce._injectChildStyle(xe);const Ke=p.subTree=yi(p);v(null,Ke,_,y,p,E,b),g.el=Ke.el}if(ne&&je(ne,E),!Ve&&(j=B&&B.onVnodeMounted)){const Ke=g;je(()=>Ge(j,te,Ke),E)}(g.shapeFlag&256||te&&Ps(te.vnode)&&te.vnode.shapeFlag&256)&&p.a&&je(p.a,E),p.isMounted=!0,g=_=y=null}};p.scope.on();const P=p.effect=new Fo(x);p.scope.off();const O=p.update=P.run.bind(P),M=p.job=P.runIfDirty.bind(P);M.i=p,M.id=p.uid,P.scheduler=()=>Kn(M),It(p,!0),O()},W=(p,g,_)=>{g.component=p;const y=p.vnode.props;p.vnode=g,p.next=null,qc(p,g.props,y,_),Vc(p,g.children,_),ct(),hi(p),ut()},X=(p,g,_,y,E,b,C,x,P=!1)=>{const O=p&&p.children,M=p?p.shapeFlag:0,j=g.children,{patchFlag:D,shapeFlag:B}=g;if(D>0){if(D&128){dt(O,j,_,y,E,b,C,x,P);return}else if(D&256){Ze(O,j,_,y,E,b,C,x,P);return}}B&8?(M&16&&Ue(O,E,b),j!==O&&c(_,j)):M&16?B&16?dt(O,j,_,y,E,b,C,x,P):Ue(O,E,b,!0):(M&8&&c(_,""),B&16&&z(j,_,y,E,b,C,x,P))},Ze=(p,g,_,y,E,b,C,x,P)=>{p=p||is,g=g||is;const O=p.length,M=g.length,j=Math.min(O,M);let D;for(D=0;D<j;D++){const B=g[D]=P?wt(g[D]):Qe(g[D]);v(p[D],B,_,null,E,b,C,x,P)}O>M?Ue(p,E,b,!0,!1,j):z(g,_,y,E,b,C,x,P,j)},dt=(p,g,_,y,E,b,C,x,P)=>{let O=0;const M=g.length;let j=p.length-1,D=M-1;for(;O<=j&&O<=D;){const B=p[O],J=g[O]=P?wt(g[O]):Qe(g[O]);if(_s(B,J))v(B,J,_,null,E,b,C,x,P);else break;O++}for(;O<=j&&O<=D;){const B=p[j],J=g[D]=P?wt(g[D]):Qe(g[D]);if(_s(B,J))v(B,J,_,null,E,b,C,x,P);else break;j--,D--}if(O>j){if(O<=D){const B=D+1,J=B<M?g[B].el:y;for(;O<=D;)v(null,g[O]=P?wt(g[O]):Qe(g[O]),_,J,E,b,C,x,P),O++}}else if(O>D)for(;O<=j;)Oe(p[O],E,b,!0),O++;else{const B=O,J=O,ne=new Map;for(O=J;O<=D;O++){const Ie=g[O]=P?wt(g[O]):Qe(g[O]);Ie.key!=null&&ne.set(Ie.key,O)}let te,Pe=0;const xe=D-J+1;let Ve=!1,Ke=0;const ps=new Array(xe);for(O=0;O<xe;O++)ps[O]=0;for(O=B;O<=j;O++){const Ie=p[O];if(Pe>=xe){Oe(Ie,E,b,!0);continue}let ze;if(Ie.key!=null)ze=ne.get(Ie.key);else for(te=J;te<=D;te++)if(ps[te-J]===0&&_s(Ie,g[te])){ze=te;break}ze===void 0?Oe(Ie,E,b,!0):(ps[ze-J]=O+1,ze>=Ke?Ke=ze:Ve=!0,v(Ie,g[ze],_,null,E,b,C,x,P),Pe++)}const oi=Ve?Jc(ps):is;for(te=oi.length-1,O=xe-1;O>=0;O--){const Ie=J+O,ze=g[Ie],ai=Ie+1<M?g[Ie+1].el:y;ps[O]===0?v(null,ze,_,ai,E,b,C,x,P):Ve&&(te<0||O!==oi[te]?We(ze,_,ai,2):te--)}}},We=(p,g,_,y,E=null)=>{const{el:b,type:C,transition:x,children:P,shapeFlag:O}=p;if(O&6){We(p.component.subTree,g,_,y);return}if(O&128){p.suspense.move(g,_,y);return}if(O&64){C.move(p,g,_,U);return}if(C===it){r(b,g,_);for(let j=0;j<P.length;j++)We(P[j],g,_,y);r(p.anchor,g,_);return}if(C===hr){S(p,g,_);return}if(y!==2&&O&1&&x)if(y===0)x.beforeEnter(b),r(b,g,_),je(()=>x.enter(b),E);else{const{leave:j,delayLeave:D,afterLeave:B}=x,J=()=>{p.ctx.isUnmounted?n(b):r(b,g,_)},ne=()=>{j(b,()=>{J(),B&&B()})};D?D(b,J,ne):ne()}else r(b,g,_)},Oe=(p,g,_,y=!1,E=!1)=>{const{type:b,props:C,ref:x,children:P,dynamicChildren:O,shapeFlag:M,patchFlag:j,dirs:D,cacheIndex:B}=p;if(j===-2&&(E=!1),x!=null&&(ct(),Os(x,null,_,p,!0),ut()),B!=null&&(g.renderCache[B]=void 0),M&256){g.ctx.deactivate(p);return}const J=M&1&&D,ne=!Ps(p);let te;if(ne&&(te=C&&C.onVnodeBeforeUnmount)&&Ge(te,g,p),M&6)Xs(p.component,_,y);else{if(M&128){p.suspense.unmount(_,y);return}J&&Ct(p,null,g,"beforeUnmount"),M&64?p.type.remove(p,g,_,U,y):O&&!O.hasOnce&&(b!==it||j>0&&j&64)?Ue(O,g,_,!1,!0):(b===it&&j&384||!E&&M&16)&&Ue(P,g,_),y&&Ft(p)}(ne&&(te=C&&C.onVnodeUnmounted)||J)&&je(()=>{te&&Ge(te,g,p),J&&Ct(p,null,g,"unmounted")},_)},Ft=p=>{const{type:g,el:_,anchor:y,transition:E}=p;if(g===it){Ht(_,y);return}if(g===hr){T(p);return}const b=()=>{n(_),E&&!E.persisted&&E.afterLeave&&E.afterLeave()};if(p.shapeFlag&1&&E&&!E.persisted){const{leave:C,delayLeave:x}=E,P=()=>C(_,b);x?x(p.el,b,P):P()}else b()},Ht=(p,g)=>{let _;for(;p!==g;)_=d(p),n(p),p=_;n(g)},Xs=(p,g,_)=>{const{bum:y,scope:E,job:b,subTree:C,um:x,m:P,a:O,parent:M,slots:{__:j}}=p;vi(P),vi(O),y&&cr(y),M&&N(j)&&j.forEach(D=>{M.renderCache[D]=void 0}),E.stop(),b&&(b.flags|=8,Oe(C,p,g,_)),x&&je(x,g),je(()=>{p.isUnmounted=!0},g),g&&g.pendingBranch&&!g.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===g.pendingId&&(g.deps--,g.deps===0&&g.resolve())},Ue=(p,g,_,y=!1,E=!1,b=0)=>{for(let C=b;C<p.length;C++)Oe(p[C],g,_,y,E)},w=p=>{if(p.shapeFlag&6)return w(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const g=d(p.anchor||p.el),_=g&&g[gc];return _?d(_):g};let $=!1;const I=(p,g,_)=>{p==null?g._vnode&&Oe(g._vnode,null,null,!0):v(g._vnode||null,p,g,null,null,null,_),g._vnode=p,$||($=!0,hi(),la(),$=!1)},U={p:v,um:Oe,m:We,r:Ft,mt:Rt,mc:z,pc:X,pbc:ce,n:w,o:e};return{render:I,hydrate:void 0,createApp:Mc(I)}}function Vr({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function It({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Gc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Pa(e,t,s=!1){const r=e.children,n=t.children;if(N(r)&&N(n))for(let i=0;i<r.length;i++){const o=r[i];let a=n[i];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=n[i]=wt(n[i]),a.el=o.el),!s&&a.patchFlag!==-2&&Pa(o,a)),a.type===jr&&(a.el=o.el),a.type===Pt&&!a.el&&(a.el=o.el)}}function Jc(e){const t=e.slice(),s=[0];let r,n,i,o,a;const l=e.length;for(r=0;r<l;r++){const u=e[r];if(u!==0){if(n=s[s.length-1],e[n]<u){t[r]=n,s.push(r);continue}for(i=0,o=s.length-1;i<o;)a=i+o>>1,e[s[a]]<u?i=a+1:o=a;u<e[s[i]]&&(i>0&&(t[r]=s[i-1]),s[i]=r)}}for(i=s.length,o=s[i-1];i-- >0;)s[i]=o,o=t[o];return s}function xa(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:xa(t)}function vi(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Qc=Symbol.for("v-scx"),Yc=()=>Me(Qc);function As(e,t,s){return Aa(e,t,s)}function Aa(e,t,s=re){const{immediate:r,deep:n,flush:i,once:o}=s,a=we({},s),l=t&&r||!t&&i!=="post";let u;if(Ws){if(i==="sync"){const f=Yc();u=f.__watcherHandles||(f.__watcherHandles=[])}else if(!l){const f=()=>{};return f.stop=Ye,f.resume=Ye,f.pause=Ye,f}}const c=_e;a.call=(f,m,v)=>Xe(f,c,m,v);let h=!1;i==="post"?a.scheduler=f=>{je(f,c&&c.suspense)}:i!=="sync"&&(h=!0,a.scheduler=(f,m)=>{m?f():Kn(f)}),a.augmentJob=f=>{t&&(f.flags|=4),h&&(f.flags|=2,c&&(f.id=c.uid,f.i=c))};const d=uc(e,t,a);return Ws&&(u?u.push(d):l&&d()),d}function Xc(e,t,s){const r=this.proxy,n=he(e)?e.includes(".")?Ra(r,e):()=>r[e]:e.bind(r,r);let i;F(t)?i=t:(i=t.handler,s=t);const o=Qs(this),a=Aa(n,i.bind(r),s);return o(),a}function Ra(e,t){const s=t.split(".");return()=>{let r=e;for(let n=0;n<s.length&&r;n++)r=r[s[n]];return r}}const Zc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ne(t)}Modifiers`]||e[`${Bt(t)}Modifiers`];function eu(e,t,...s){if(e.isUnmounted)return;const r=e.vnode.props||re;let n=s;const i=t.startsWith("update:"),o=i&&Zc(r,t.slice(7));o&&(o.trim&&(n=s.map(c=>he(c)?c.trim():c)),o.number&&(n=s.map(an)));let a,l=r[a=Nr(t)]||r[a=Nr(Ne(t))];!l&&i&&(l=r[a=Nr(Bt(t))]),l&&Xe(l,e,6,n);const u=r[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Xe(u,e,6,n)}}function Ca(e,t,s=!1){const r=t.emitsCache,n=r.get(e);if(n!==void 0)return n;const i=e.emits;let o={},a=!1;if(!F(e)){const l=u=>{const c=Ca(u,t,!0);c&&(a=!0,we(o,c))};!s&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!i&&!a?(le(e)&&r.set(e,null),null):(N(i)?i.forEach(l=>o[l]=null):we(o,i),le(e)&&r.set(e,o),o)}function Ir(e,t){return!e||!kr(t)?!1:(t=t.slice(2).replace(/Once$/,""),ee(e,t[0].toLowerCase()+t.slice(1))||ee(e,Bt(t))||ee(e,t))}function yi(e){const{type:t,vnode:s,proxy:r,withProxy:n,propsOptions:[i],slots:o,attrs:a,emit:l,render:u,renderCache:c,props:h,data:d,setupState:f,ctx:m,inheritAttrs:v}=e,k=vr(e);let A,R;try{if(s.shapeFlag&4){const T=n||r,L=T;A=Qe(u.call(L,T,c,h,f,d,m)),R=a}else{const T=t;A=Qe(T.length>1?T(h,{attrs:a,slots:o,emit:l}):T(h,null)),R=t.props?a:tu(a)}}catch(T){Rs.length=0,Rr(T,e,1),A=ke(Pt)}let S=A;if(R&&v!==!1){const T=Object.keys(R),{shapeFlag:L}=S;T.length&&L&7&&(i&&T.some(In)&&(R=su(R,i)),S=cs(S,R,!1,!0))}return s.dirs&&(S=cs(S,null,!1,!0),S.dirs=S.dirs?S.dirs.concat(s.dirs):s.dirs),s.transition&&zn(S,s.transition),A=S,vr(k),A}const tu=e=>{let t;for(const s in e)(s==="class"||s==="style"||kr(s))&&((t||(t={}))[s]=e[s]);return t},su=(e,t)=>{const s={};for(const r in e)(!In(r)||!(r.slice(9)in t))&&(s[r]=e[r]);return s};function ru(e,t,s){const{props:r,children:n,component:i}=e,{props:o,children:a,patchFlag:l}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&l>=0){if(l&1024)return!0;if(l&16)return r?wi(r,o,u):!!o;if(l&8){const c=t.dynamicProps;for(let h=0;h<c.length;h++){const d=c[h];if(o[d]!==r[d]&&!Ir(u,d))return!0}}}else return(n||a)&&(!a||!a.$stable)?!0:r===o?!1:r?o?wi(r,o,u):!0:!!o;return!1}function wi(e,t,s){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let n=0;n<r.length;n++){const i=r[n];if(t[i]!==e[i]&&!Ir(s,i))return!0}return!1}function nu({vnode:e,parent:t},s){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=s,t=t.parent;else break}}const Ia=e=>e.__isSuspense;function iu(e,t){t&&t.pendingBranch?N(e)?t.effects.push(...e):t.effects.push(e):dc(e)}const it=Symbol.for("v-fgt"),jr=Symbol.for("v-txt"),Pt=Symbol.for("v-cmt"),hr=Symbol.for("v-stc"),Rs=[];let Le=null;function gn(e=!1){Rs.push(Le=e?null:[])}function ou(){Rs.pop(),Le=Rs[Rs.length-1]||null}let Hs=1;function bi(e,t=!1){Hs+=e,e<0&&Le&&t&&(Le.hasOnce=!0)}function ja(e){return e.dynamicChildren=Hs>0?Le||is:null,ou(),Hs>0&&Le&&Le.push(e),e}function Si(e,t,s,r,n,i){return ja(Cs(e,t,s,r,n,i,!0))}function au(e,t,s,r,n){return ja(ke(e,t,s,r,n,!0))}function wr(e){return e?e.__v_isVNode===!0:!1}function _s(e,t){return e.type===t.type&&e.key===t.key}const $a=({key:e})=>e??null,fr=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?he(e)||ue(e)||F(e)?{i:Re,r:e,k:t,f:!!s}:e:null);function Cs(e,t=null,s=null,r=0,n=null,i=e===it?0:1,o=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&$a(t),ref:t&&fr(t),scopeId:ua,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:Re};return a?(Yn(l,s),i&128&&e.normalize(l)):s&&(l.shapeFlag|=he(s)?8:16),Hs>0&&!o&&Le&&(l.patchFlag>0||i&6)&&l.patchFlag!==32&&Le.push(l),l}const ke=lu;function lu(e,t=null,s=null,r=0,n=null,i=!1){if((!e||e===Ac)&&(e=Pt),wr(e)){const a=cs(e,t,!0);return s&&Yn(a,s),Hs>0&&!i&&Le&&(a.shapeFlag&6?Le[Le.indexOf(e)]=a:Le.push(a)),a.patchFlag=-2,a}if(bu(e)&&(e=e.__vccOpts),t){t=cu(t);let{class:a,style:l}=t;a&&!he(a)&&(t.class=Un(a)),le(l)&&(Hn(l)&&!N(l)&&(l=we({},l)),t.style=Ln(l))}const o=he(e)?1:Ia(e)?128:_c(e)?64:le(e)?4:F(e)?2:0;return Cs(e,t,s,r,n,o,i,!0)}function cu(e){return e?Hn(e)||ba(e)?we({},e):e:null}function cs(e,t,s=!1,r=!1){const{props:n,ref:i,patchFlag:o,children:a,transition:l}=e,u=t?fu(n||{},t):n,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&$a(u),ref:t&&t.ref?s&&i?N(i)?i.concat(fr(t)):[i,fr(t)]:fr(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==it?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&cs(e.ssContent),ssFallback:e.ssFallback&&cs(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&zn(c,l.clone(c)),c}function uu(e=" ",t=0){return ke(jr,null,e,t)}function wp(e,t){const s=ke(hr,null,e);return s.staticCount=t,s}function hu(e="",t=!1){return t?(gn(),au(Pt,null,e)):ke(Pt,null,e)}function Qe(e){return e==null||typeof e=="boolean"?ke(Pt):N(e)?ke(it,null,e.slice()):wr(e)?wt(e):ke(jr,null,String(e))}function wt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:cs(e)}function Yn(e,t){let s=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(N(t))s=16;else if(typeof t=="object")if(r&65){const n=t.default;n&&(n._c&&(n._d=!1),Yn(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!ba(t)?t._ctx=Re:n===3&&Re&&(Re.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else F(t)?(t={default:t,_ctx:Re},s=32):(t=String(t),r&64?(s=16,t=[uu(t)]):s=8);e.children=t,e.shapeFlag|=s}function fu(...e){const t={};for(let s=0;s<e.length;s++){const r=e[s];for(const n in r)if(n==="class")t.class!==r.class&&(t.class=Un([t.class,r.class]));else if(n==="style")t.style=Ln([t.style,r.style]);else if(kr(n)){const i=t[n],o=r[n];o&&i!==o&&!(N(i)&&i.includes(o))&&(t[n]=i?[].concat(i,o):o)}else n!==""&&(t[n]=r[n])}return t}function Ge(e,t,s,r=null){Xe(e,t,7,[s,r])}const du=va();let pu=0;function gu(e,t,s){const r=e.type,n=(t?t.appContext:e.appContext)||du,i={uid:pu++,vnode:e,type:r,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new No(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ea(r,n),emitsOptions:Ca(r,n),emit:null,emitted:null,propsDefaults:re,inheritAttrs:r.inheritAttrs,ctx:re,data:re,props:re,attrs:re,slots:re,refs:re,setupState:re,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=eu.bind(null,i),e.ce&&e.ce(i),i}let _e=null,br,_n;{const e=xr(),t=(s,r)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(r),i=>{n.length>1?n.forEach(o=>o(i)):n[0](i)}};br=t("__VUE_INSTANCE_SETTERS__",s=>_e=s),_n=t("__VUE_SSR_SETTERS__",s=>Ws=s)}const Qs=e=>{const t=_e;return br(e),e.scope.on(),()=>{e.scope.off(),br(t)}},Ei=()=>{_e&&_e.scope.off(),br(null)};function La(e){return e.vnode.shapeFlag&4}let Ws=!1;function _u(e,t=!1,s=!1){t&&_n(t);const{props:r,children:n}=e.vnode,i=La(e);Bc(e,r,i,t),Wc(e,n,s||t);const o=i?mu(e,t):void 0;return t&&_n(!1),o}function mu(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Cc);const{setup:r}=s;if(r){ct();const n=e.setupContext=r.length>1?yu(e):null,i=Qs(e),o=Js(r,e,0,[e.props,n]),a=jo(o);if(ut(),i(),(a||e.sp)&&!Ps(e)&&ha(e),a){if(o.then(Ei,Ei),t)return o.then(l=>{ki(e,l)}).catch(l=>{Rr(l,e,0)});e.asyncDep=o}else ki(e,o)}else Ua(e)}function ki(e,t,s){F(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:le(t)&&(e.setupState=ia(t)),Ua(e)}function Ua(e,t,s){const r=e.type;e.render||(e.render=r.render||Ye);{const n=Qs(e);ct();try{Ic(e)}finally{ut(),n()}}}const vu={get(e,t){return ye(e,"get",""),e[t]}};function yu(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,vu),slots:e.slots,emit:e.emit,expose:t}}function $r(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ia(Wn(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in xs)return xs[s](e)},has(t,s){return s in t||s in xs}})):e.proxy}function wu(e,t=!0){return F(e)?e.displayName||e.name:e.name||t&&e.__name}function bu(e){return F(e)&&"__vccOpts"in e}const $e=(e,t)=>lc(e,t,Ws);function Da(e,t,s){const r=arguments.length;return r===2?le(t)&&!N(t)?wr(t)?ke(e,null,[t]):ke(e,t):ke(e,null,t):(r>3?s=Array.prototype.slice.call(arguments,2):r===3&&wr(s)&&(s=[s]),ke(e,t,s))}const Su="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let mn;const Ti=typeof window<"u"&&window.trustedTypes;if(Ti)try{mn=Ti.createPolicy("vue",{createHTML:e=>e})}catch{}const Ma=mn?e=>mn.createHTML(e):e=>e,Eu="http://www.w3.org/2000/svg",ku="http://www.w3.org/1998/Math/MathML",rt=typeof document<"u"?document:null,Oi=rt&&rt.createElement("template"),Tu={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,r)=>{const n=t==="svg"?rt.createElementNS(Eu,e):t==="mathml"?rt.createElementNS(ku,e):s?rt.createElement(e,{is:s}):rt.createElement(e);return e==="select"&&r&&r.multiple!=null&&n.setAttribute("multiple",r.multiple),n},createText:e=>rt.createTextNode(e),createComment:e=>rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,r,n,i){const o=s?s.previousSibling:t.lastChild;if(n&&(n===i||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===i||!(n=n.nextSibling)););else{Oi.innerHTML=Ma(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=Oi.content;if(r==="svg"||r==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,s)}return[o?o.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Ou=Symbol("_vtc");function Pu(e,t,s){const r=e[Ou];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Pi=Symbol("_vod"),xu=Symbol("_vsh"),Au=Symbol(""),Ru=/(^|;)\s*display\s*:/;function Cu(e,t,s){const r=e.style,n=he(s);let i=!1;if(s&&!n){if(t)if(he(t))for(const o of t.split(";")){const a=o.slice(0,o.indexOf(":")).trim();s[a]==null&&dr(r,a,"")}else for(const o in t)s[o]==null&&dr(r,o,"");for(const o in s)o==="display"&&(i=!0),dr(r,o,s[o])}else if(n){if(t!==s){const o=r[Au];o&&(s+=";"+o),r.cssText=s,i=Ru.test(s)}}else t&&e.removeAttribute("style");Pi in e&&(e[Pi]=i?r.display:"",e[xu]&&(r.display="none"))}const xi=/\s*!important$/;function dr(e,t,s){if(N(s))s.forEach(r=>dr(e,t,r));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const r=Iu(e,t);xi.test(s)?e.setProperty(Bt(r),s.replace(xi,""),"important"):e[r]=s}}const Ai=["Webkit","Moz","ms"],Kr={};function Iu(e,t){const s=Kr[t];if(s)return s;let r=Ne(t);if(r!=="filter"&&r in e)return Kr[t]=r;r=Pr(r);for(let n=0;n<Ai.length;n++){const i=Ai[n]+r;if(i in e)return Kr[t]=i}return t}const Ri="http://www.w3.org/1999/xlink";function Ci(e,t,s,r,n,i=jl(t)){r&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Ri,t.slice(6,t.length)):e.setAttributeNS(Ri,t,s):s==null||i&&!Uo(s)?e.removeAttribute(t):e.setAttribute(t,i?"":xt(s)?String(s):s)}function Ii(e,t,s,r,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Ma(s):s);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const a=i==="OPTION"?e.getAttribute("value")||"":e.value,l=s==null?e.type==="checkbox"?"on":"":String(s);(a!==l||!("_value"in e))&&(e.value=l),s==null&&e.removeAttribute(t),e._value=s;return}let o=!1;if(s===""||s==null){const a=typeof e[t];a==="boolean"?s=Uo(s):s==null&&a==="string"?(s="",o=!0):a==="number"&&(s=0,o=!0)}try{e[t]=s}catch{}o&&e.removeAttribute(n||t)}function ss(e,t,s,r){e.addEventListener(t,s,r)}function ju(e,t,s,r){e.removeEventListener(t,s,r)}const ji=Symbol("_vei");function $u(e,t,s,r,n=null){const i=e[ji]||(e[ji]={}),o=i[t];if(r&&o)o.value=r;else{const[a,l]=Lu(t);if(r){const u=i[t]=Mu(r,n);ss(e,a,u,l)}else o&&(ju(e,a,o,l),i[t]=void 0)}}const $i=/(?:Once|Passive|Capture)$/;function Lu(e){let t;if($i.test(e)){t={};let r;for(;r=e.match($i);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Bt(e.slice(2)),t]}let zr=0;const Uu=Promise.resolve(),Du=()=>zr||(Uu.then(()=>zr=0),zr=Date.now());function Mu(e,t){const s=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=s.attached)return;Xe(Nu(r,s.value),t,5,[r])};return s.value=e,s.attached=Du(),s}function Nu(e,t){if(N(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(r=>n=>!n._stopped&&r&&r(n))}else return t}const Li=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Bu=(e,t,s,r,n,i)=>{const o=n==="svg";t==="class"?Pu(e,r,o):t==="style"?Cu(e,s,r):kr(t)?In(t)||$u(e,t,s,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):qu(e,t,r,o))?(Ii(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ci(e,t,r,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!he(r))?Ii(e,Ne(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Ci(e,t,r,o))};function qu(e,t,s,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Li(t)&&F(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Li(t)&&he(s)?!1:t in e}const Ui=e=>{const t=e.props["onUpdate:modelValue"]||!1;return N(t)?s=>cr(t,s):t};function Fu(e){e.target.composing=!0}function Di(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Gr=Symbol("_assign"),bp={created(e,{modifiers:{lazy:t,trim:s,number:r}},n){e[Gr]=Ui(n);const i=r||n.props&&n.props.type==="number";ss(e,t?"change":"input",o=>{if(o.target.composing)return;let a=e.value;s&&(a=a.trim()),i&&(a=an(a)),e[Gr](a)}),s&&ss(e,"change",()=>{e.value=e.value.trim()}),t||(ss(e,"compositionstart",Fu),ss(e,"compositionend",Di),ss(e,"change",Di))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:r,trim:n,number:i}},o){if(e[Gr]=Ui(o),e.composing)return;const a=(i||e.type==="number")&&!/^0\d/.test(e.value)?an(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(r&&t===s||n&&e.value.trim()===l)||(e.value=l))}},Hu=["ctrl","shift","alt","meta"],Wu={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Hu.some(s=>e[`${s}Key`]&&!t.includes(s))},Sp=(e,t)=>{const s=e._withMods||(e._withMods={}),r=t.join(".");return s[r]||(s[r]=(n,...i)=>{for(let o=0;o<t.length;o++){const a=Wu[t[o]];if(a&&a(n,t))return}return e(n,...i)})},Vu=we({patchProp:Bu},Tu);let Mi;function Ku(){return Mi||(Mi=Kc(Vu))}const zu=(...e)=>{const t=Ku().createApp(...e),{mount:s}=t;return t.mount=r=>{const n=Ju(r);if(!n)return;const i=t._component;!F(i)&&!i.render&&!i.template&&(i.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const o=s(n,!1,Gu(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),o},t};function Gu(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Ju(e){return he(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Na;const Lr=e=>Na=e,Ba=Symbol();function vn(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Is;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Is||(Is={}));function Qu(){const e=Bo(!0),t=e.run(()=>as({}));let s=[],r=[];const n=Wn({install(i){Lr(n),n._a=i,i.provide(Ba,n),i.config.globalProperties.$pinia=n,r.forEach(o=>s.push(o)),r=[]},use(i){return this._a?s.push(i):r.push(i),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return n}const qa=()=>{};function Ni(e,t,s,r=qa){e.push(t);const n=()=>{const i=e.indexOf(t);i>-1&&(e.splice(i,1),r())};return!s&&qo()&&Ll(n),n}function Vt(e,...t){e.slice().forEach(s=>{s(...t)})}const Yu=e=>e(),Bi=Symbol(),Jr=Symbol();function yn(e,t){e instanceof Map&&t instanceof Map?t.forEach((s,r)=>e.set(r,s)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const s in t){if(!t.hasOwnProperty(s))continue;const r=t[s],n=e[s];vn(n)&&vn(r)&&e.hasOwnProperty(s)&&!ue(r)&&!Tt(r)?e[s]=yn(n,r):e[s]=r}return e}const Xu=Symbol();function Zu(e){return!vn(e)||!Object.prototype.hasOwnProperty.call(e,Xu)}const{assign:mt}=Object;function eh(e){return!!(ue(e)&&e.effect)}function th(e,t,s,r){const{state:n,actions:i,getters:o}=t,a=s.state.value[e];let l;function u(){a||(s.state.value[e]=n?n():{});const c=nc(s.state.value[e]);return mt(c,i,Object.keys(o||{}).reduce((h,d)=>(h[d]=Wn($e(()=>{Lr(s);const f=s._s.get(e);return o[d].call(f,f)})),h),{}))}return l=Fa(e,u,t,s,r,!0),l}function Fa(e,t,s={},r,n,i){let o;const a=mt({actions:{}},s),l={deep:!0};let u,c,h=[],d=[],f;const m=r.state.value[e];!i&&!m&&(r.state.value[e]={}),as({});let v;function k(z){let G;u=c=!1,typeof z=="function"?(z(r.state.value[e]),G={type:Is.patchFunction,storeId:e,events:f}):(yn(r.state.value[e],z),G={type:Is.patchObject,payload:z,storeId:e,events:f});const ce=v=Symbol();Vn().then(()=>{v===ce&&(u=!0)}),c=!0,Vt(h,G,r.state.value[e])}const A=i?function(){const{state:G}=s,ce=G?G():{};this.$patch(be=>{mt(be,ce)})}:qa;function R(){o.stop(),h=[],d=[],r._s.delete(e)}const S=(z,G="")=>{if(Bi in z)return z[Jr]=G,z;const ce=function(){Lr(r);const be=Array.from(arguments),Ce=[],Te=[];function Rt(W){Ce.push(W)}function ft(W){Te.push(W)}Vt(d,{args:be,name:ce[Jr],store:L,after:Rt,onError:ft});let ae;try{ae=z.apply(this&&this.$id===e?this:L,be)}catch(W){throw Vt(Te,W),W}return ae instanceof Promise?ae.then(W=>(Vt(Ce,W),W)).catch(W=>(Vt(Te,W),Promise.reject(W))):(Vt(Ce,ae),ae)};return ce[Bi]=!0,ce[Jr]=G,ce},T={_p:r,$id:e,$onAction:Ni.bind(null,d),$patch:k,$reset:A,$subscribe(z,G={}){const ce=Ni(h,z,G.detached,()=>be()),be=o.run(()=>As(()=>r.state.value[e],Ce=>{(G.flush==="sync"?c:u)&&z({storeId:e,type:Is.direct,events:f},Ce)},mt({},l,G)));return ce},$dispose:R},L=Gs(T);r._s.set(e,L);const q=(r._a&&r._a.runWithContext||Yu)(()=>r._e.run(()=>(o=Bo()).run(()=>t({action:S}))));for(const z in q){const G=q[z];if(ue(G)&&!eh(G)||Tt(G))i||(m&&Zu(G)&&(ue(G)?G.value=m[z]:yn(G,m[z])),r.state.value[e][z]=G);else if(typeof G=="function"){const ce=S(G,z);q[z]=ce,a.actions[z]=G}}return mt(L,q),mt(Y(L),q),Object.defineProperty(L,"$state",{get:()=>r.state.value[e],set:z=>{k(G=>{mt(G,z)})}}),r._p.forEach(z=>{mt(L,o.run(()=>z({store:L,app:r._a,pinia:r,options:a})))}),m&&i&&s.hydrate&&s.hydrate(L.$state,m),u=!0,c=!0,L}/*! #__NO_SIDE_EFFECTS__ */function sh(e,t,s){let r;const n=typeof t=="function";r=n?s:t;function i(o,a){const l=Nc();return o=o||(l?Me(Ba,null):null),o&&Lr(o),o=Na,o._s.has(e)||(n?Fa(e,t,r,o):th(e,r,o)),o._s.get(e)}return i.$id=e,i}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const rs=typeof document<"u";function Ha(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function rh(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Ha(e.default)}const Z=Object.assign;function Qr(e,t){const s={};for(const r in t){const n=t[r];s[r]=He(n)?n.map(e):e(n)}return s}const js=()=>{},He=Array.isArray,Wa=/#/g,nh=/&/g,ih=/\//g,oh=/=/g,ah=/\?/g,Va=/\+/g,lh=/%5B/g,ch=/%5D/g,Ka=/%5E/g,uh=/%60/g,za=/%7B/g,hh=/%7C/g,Ga=/%7D/g,fh=/%20/g;function Xn(e){return encodeURI(""+e).replace(hh,"|").replace(lh,"[").replace(ch,"]")}function dh(e){return Xn(e).replace(za,"{").replace(Ga,"}").replace(Ka,"^")}function wn(e){return Xn(e).replace(Va,"%2B").replace(fh,"+").replace(Wa,"%23").replace(nh,"%26").replace(uh,"`").replace(za,"{").replace(Ga,"}").replace(Ka,"^")}function ph(e){return wn(e).replace(oh,"%3D")}function gh(e){return Xn(e).replace(Wa,"%23").replace(ah,"%3F")}function _h(e){return e==null?"":gh(e).replace(ih,"%2F")}function Vs(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const mh=/\/$/,vh=e=>e.replace(mh,"");function Yr(e,t,s="/"){let r,n={},i="",o="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),n=e(i)),a>-1&&(r=r||t.slice(0,a),o=t.slice(a,t.length)),r=Sh(r??t,s),{fullPath:r+(i&&"?")+i+o,path:r,query:n,hash:Vs(o)}}function yh(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function qi(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function wh(e,t,s){const r=t.matched.length-1,n=s.matched.length-1;return r>-1&&r===n&&us(t.matched[r],s.matched[n])&&Ja(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function us(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ja(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!bh(e[s],t[s]))return!1;return!0}function bh(e,t){return He(e)?Fi(e,t):He(t)?Fi(t,e):e===t}function Fi(e,t){return He(t)?e.length===t.length&&e.every((s,r)=>s===t[r]):e.length===1&&e[0]===t}function Sh(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),r=e.split("/"),n=r[r.length-1];(n===".."||n===".")&&r.push("");let i=s.length-1,o,a;for(o=0;o<r.length;o++)if(a=r[o],a!==".")if(a==="..")i>1&&i--;else break;return s.slice(0,i).join("/")+"/"+r.slice(o).join("/")}const pt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ks;(function(e){e.pop="pop",e.push="push"})(Ks||(Ks={}));var $s;(function(e){e.back="back",e.forward="forward",e.unknown=""})($s||($s={}));function Eh(e){if(!e)if(rs){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),vh(e)}const kh=/^[^#]+#/;function Th(e,t){return e.replace(kh,"#")+t}function Oh(e,t){const s=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-s.left-(t.left||0),top:r.top-s.top-(t.top||0)}}const Ur=()=>({left:window.scrollX,top:window.scrollY});function Ph(e){let t;if("el"in e){const s=e.el,r=typeof s=="string"&&s.startsWith("#"),n=typeof s=="string"?r?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n)return;t=Oh(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Hi(e,t){return(history.state?history.state.position-t:-1)+e}const bn=new Map;function xh(e,t){bn.set(e,t)}function Ah(e){const t=bn.get(e);return bn.delete(e),t}let Rh=()=>location.protocol+"//"+location.host;function Qa(e,t){const{pathname:s,search:r,hash:n}=t,i=e.indexOf("#");if(i>-1){let a=n.includes(e.slice(i))?e.slice(i).length:1,l=n.slice(a);return l[0]!=="/"&&(l="/"+l),qi(l,"")}return qi(s,e)+r+n}function Ch(e,t,s,r){let n=[],i=[],o=null;const a=({state:d})=>{const f=Qa(e,location),m=s.value,v=t.value;let k=0;if(d){if(s.value=f,t.value=d,o&&o===m){o=null;return}k=v?d.position-v.position:0}else r(f);n.forEach(A=>{A(s.value,m,{delta:k,type:Ks.pop,direction:k?k>0?$s.forward:$s.back:$s.unknown})})};function l(){o=s.value}function u(d){n.push(d);const f=()=>{const m=n.indexOf(d);m>-1&&n.splice(m,1)};return i.push(f),f}function c(){const{history:d}=window;d.state&&d.replaceState(Z({},d.state,{scroll:Ur()}),"")}function h(){for(const d of i)d();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:u,destroy:h}}function Wi(e,t,s,r=!1,n=!1){return{back:e,current:t,forward:s,replaced:r,position:window.history.length,scroll:n?Ur():null}}function Ih(e){const{history:t,location:s}=window,r={value:Qa(e,s)},n={value:t.state};n.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(l,u,c){const h=e.indexOf("#"),d=h>-1?(s.host&&document.querySelector("base")?e:e.slice(h))+l:Rh()+e+l;try{t[c?"replaceState":"pushState"](u,"",d),n.value=u}catch(f){console.error(f),s[c?"replace":"assign"](d)}}function o(l,u){const c=Z({},t.state,Wi(n.value.back,l,n.value.forward,!0),u,{position:n.value.position});i(l,c,!0),r.value=l}function a(l,u){const c=Z({},n.value,t.state,{forward:l,scroll:Ur()});i(c.current,c,!0);const h=Z({},Wi(r.value,l,null),{position:c.position+1},u);i(l,h,!1),r.value=l}return{location:r,state:n,push:a,replace:o}}function jh(e){e=Eh(e);const t=Ih(e),s=Ch(e,t.state,t.location,t.replace);function r(i,o=!0){o||s.pauseListeners(),history.go(i)}const n=Z({location:"",base:e,go:r,createHref:Th.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function $h(e){return typeof e=="string"||e&&typeof e=="object"}function Ya(e){return typeof e=="string"||typeof e=="symbol"}const Xa=Symbol("");var Vi;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Vi||(Vi={}));function hs(e,t){return Z(new Error,{type:e,[Xa]:!0},t)}function tt(e,t){return e instanceof Error&&Xa in e&&(t==null||!!(e.type&t))}const Ki="[^/]+?",Lh={sensitive:!1,strict:!1,start:!0,end:!0},Uh=/[.+*?^${}()[\]/\\]/g;function Dh(e,t){const s=Z({},Lh,t),r=[];let n=s.start?"^":"";const i=[];for(const u of e){const c=u.length?[]:[90];s.strict&&!u.length&&(n+="/");for(let h=0;h<u.length;h++){const d=u[h];let f=40+(s.sensitive?.25:0);if(d.type===0)h||(n+="/"),n+=d.value.replace(Uh,"\\$&"),f+=40;else if(d.type===1){const{value:m,repeatable:v,optional:k,regexp:A}=d;i.push({name:m,repeatable:v,optional:k});const R=A||Ki;if(R!==Ki){f+=10;try{new RegExp(`(${R})`)}catch(T){throw new Error(`Invalid custom RegExp for param "${m}" (${R}): `+T.message)}}let S=v?`((?:${R})(?:/(?:${R}))*)`:`(${R})`;h||(S=k&&u.length<2?`(?:/${S})`:"/"+S),k&&(S+="?"),n+=S,f+=20,k&&(f+=-8),v&&(f+=-20),R===".*"&&(f+=-50)}c.push(f)}r.push(c)}if(s.strict&&s.end){const u=r.length-1;r[u][r[u].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const o=new RegExp(n,s.sensitive?"":"i");function a(u){const c=u.match(o),h={};if(!c)return null;for(let d=1;d<c.length;d++){const f=c[d]||"",m=i[d-1];h[m.name]=f&&m.repeatable?f.split("/"):f}return h}function l(u){let c="",h=!1;for(const d of e){(!h||!c.endsWith("/"))&&(c+="/"),h=!1;for(const f of d)if(f.type===0)c+=f.value;else if(f.type===1){const{value:m,repeatable:v,optional:k}=f,A=m in u?u[m]:"";if(He(A)&&!v)throw new Error(`Provided param "${m}" is an array but it is not repeatable (* or + modifiers)`);const R=He(A)?A.join("/"):A;if(!R)if(k)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):h=!0);else throw new Error(`Missing required param "${m}"`);c+=R}}return c||"/"}return{re:o,score:r,keys:i,parse:a,stringify:l}}function Mh(e,t){let s=0;for(;s<e.length&&s<t.length;){const r=t[s]-e[s];if(r)return r;s++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Za(e,t){let s=0;const r=e.score,n=t.score;for(;s<r.length&&s<n.length;){const i=Mh(r[s],n[s]);if(i)return i;s++}if(Math.abs(n.length-r.length)===1){if(zi(r))return 1;if(zi(n))return-1}return n.length-r.length}function zi(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Nh={type:0,value:""},Bh=/[a-zA-Z0-9_]/;function qh(e){if(!e)return[[]];if(e==="/")return[[Nh]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(f){throw new Error(`ERR (${s})/"${u}": ${f}`)}let s=0,r=s;const n=[];let i;function o(){i&&n.push(i),i=[]}let a=0,l,u="",c="";function h(){u&&(s===0?i.push({type:0,value:u}):s===1||s===2||s===3?(i.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:u,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function d(){u+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&s!==2){r=s,s=4;continue}switch(s){case 0:l==="/"?(u&&h(),o()):l===":"?(h(),s=1):d();break;case 4:d(),s=r;break;case 1:l==="("?s=2:Bh.test(l)?d():(h(),s=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:s=3:c+=l;break;case 3:h(),s=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${u}"`),h(),o(),n}function Fh(e,t,s){const r=Dh(qh(e.path),s),n=Z(r,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function Hh(e,t){const s=[],r=new Map;t=Yi({strict:!1,end:!0,sensitive:!1},t);function n(h){return r.get(h)}function i(h,d,f){const m=!f,v=Ji(h);v.aliasOf=f&&f.record;const k=Yi(t,h),A=[v];if("alias"in h){const T=typeof h.alias=="string"?[h.alias]:h.alias;for(const L of T)A.push(Ji(Z({},v,{components:f?f.record.components:v.components,path:L,aliasOf:f?f.record:v})))}let R,S;for(const T of A){const{path:L}=T;if(d&&L[0]!=="/"){const V=d.record.path,q=V[V.length-1]==="/"?"":"/";T.path=d.record.path+(L&&q+L)}if(R=Fh(T,d,k),f?f.alias.push(R):(S=S||R,S!==R&&S.alias.push(R),m&&h.name&&!Qi(R)&&o(h.name)),el(R)&&l(R),v.children){const V=v.children;for(let q=0;q<V.length;q++)i(V[q],R,f&&f.children[q])}f=f||R}return S?()=>{o(S)}:js}function o(h){if(Ya(h)){const d=r.get(h);d&&(r.delete(h),s.splice(s.indexOf(d),1),d.children.forEach(o),d.alias.forEach(o))}else{const d=s.indexOf(h);d>-1&&(s.splice(d,1),h.record.name&&r.delete(h.record.name),h.children.forEach(o),h.alias.forEach(o))}}function a(){return s}function l(h){const d=Kh(h,s);s.splice(d,0,h),h.record.name&&!Qi(h)&&r.set(h.record.name,h)}function u(h,d){let f,m={},v,k;if("name"in h&&h.name){if(f=r.get(h.name),!f)throw hs(1,{location:h});k=f.record.name,m=Z(Gi(d.params,f.keys.filter(S=>!S.optional).concat(f.parent?f.parent.keys.filter(S=>S.optional):[]).map(S=>S.name)),h.params&&Gi(h.params,f.keys.map(S=>S.name))),v=f.stringify(m)}else if(h.path!=null)v=h.path,f=s.find(S=>S.re.test(v)),f&&(m=f.parse(v),k=f.record.name);else{if(f=d.name?r.get(d.name):s.find(S=>S.re.test(d.path)),!f)throw hs(1,{location:h,currentLocation:d});k=f.record.name,m=Z({},d.params,h.params),v=f.stringify(m)}const A=[];let R=f;for(;R;)A.unshift(R.record),R=R.parent;return{name:k,path:v,params:m,matched:A,meta:Vh(A)}}e.forEach(h=>i(h));function c(){s.length=0,r.clear()}return{addRoute:i,resolve:u,removeRoute:o,clearRoutes:c,getRoutes:a,getRecordMatcher:n}}function Gi(e,t){const s={};for(const r of t)r in e&&(s[r]=e[r]);return s}function Ji(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Wh(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Wh(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const r in e.components)t[r]=typeof s=="object"?s[r]:s;return t}function Qi(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Vh(e){return e.reduce((t,s)=>Z(t,s.meta),{})}function Yi(e,t){const s={};for(const r in e)s[r]=r in t?t[r]:e[r];return s}function Kh(e,t){let s=0,r=t.length;for(;s!==r;){const i=s+r>>1;Za(e,t[i])<0?r=i:s=i+1}const n=zh(e);return n&&(r=t.lastIndexOf(n,r-1)),r}function zh(e){let t=e;for(;t=t.parent;)if(el(t)&&Za(e,t)===0)return t}function el({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Gh(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<r.length;++n){const i=r[n].replace(Va," "),o=i.indexOf("="),a=Vs(o<0?i:i.slice(0,o)),l=o<0?null:Vs(i.slice(o+1));if(a in t){let u=t[a];He(u)||(u=t[a]=[u]),u.push(l)}else t[a]=l}return t}function Xi(e){let t="";for(let s in e){const r=e[s];if(s=ph(s),r==null){r!==void 0&&(t+=(t.length?"&":"")+s);continue}(He(r)?r.map(i=>i&&wn(i)):[r&&wn(r)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+s,i!=null&&(t+="="+i))})}return t}function Jh(e){const t={};for(const s in e){const r=e[s];r!==void 0&&(t[s]=He(r)?r.map(n=>n==null?null:""+n):r==null?r:""+r)}return t}const Qh=Symbol(""),Zi=Symbol(""),Dr=Symbol(""),Zn=Symbol(""),Sn=Symbol("");function ms(){let e=[];function t(r){return e.push(r),()=>{const n=e.indexOf(r);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function bt(e,t,s,r,n,i=o=>o()){const o=r&&(r.enterCallbacks[n]=r.enterCallbacks[n]||[]);return()=>new Promise((a,l)=>{const u=d=>{d===!1?l(hs(4,{from:s,to:t})):d instanceof Error?l(d):$h(d)?l(hs(2,{from:t,to:d})):(o&&r.enterCallbacks[n]===o&&typeof d=="function"&&o.push(d),a())},c=i(()=>e.call(r&&r.instances[n],t,s,u));let h=Promise.resolve(c);e.length<3&&(h=h.then(u)),h.catch(d=>l(d))})}function Xr(e,t,s,r,n=i=>i()){const i=[];for(const o of e)for(const a in o.components){let l=o.components[a];if(!(t!=="beforeRouteEnter"&&!o.instances[a]))if(Ha(l)){const c=(l.__vccOpts||l)[t];c&&i.push(bt(c,s,r,o,a,n))}else{let u=l();i.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${o.path}"`);const h=rh(c)?c.default:c;o.mods[a]=c,o.components[a]=h;const f=(h.__vccOpts||h)[t];return f&&bt(f,s,r,o,a,n)()}))}}return i}function eo(e){const t=Me(Dr),s=Me(Zn),r=$e(()=>{const l=lt(e.to);return t.resolve(l)}),n=$e(()=>{const{matched:l}=r.value,{length:u}=l,c=l[u-1],h=s.matched;if(!c||!h.length)return-1;const d=h.findIndex(us.bind(null,c));if(d>-1)return d;const f=to(l[u-2]);return u>1&&to(c)===f&&h[h.length-1].path!==f?h.findIndex(us.bind(null,l[u-2])):d}),i=$e(()=>n.value>-1&&tf(s.params,r.value.params)),o=$e(()=>n.value>-1&&n.value===s.matched.length-1&&Ja(s.params,r.value.params));function a(l={}){if(ef(l)){const u=t[lt(e.replace)?"replace":"push"](lt(e.to)).catch(js);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:r,href:$e(()=>r.value.href),isActive:i,isExactActive:o,navigate:a}}function Yh(e){return e.length===1?e[0]:e}const Xh=Gn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:eo,setup(e,{slots:t}){const s=Gs(eo(e)),{options:r}=Me(Dr),n=$e(()=>({[so(e.activeClass,r.linkActiveClass,"router-link-active")]:s.isActive,[so(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const i=t.default&&Yh(t.default(s));return e.custom?i:Da("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},i)}}}),Zh=Xh;function ef(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function tf(e,t){for(const s in t){const r=t[s],n=e[s];if(typeof r=="string"){if(r!==n)return!1}else if(!He(n)||n.length!==r.length||r.some((i,o)=>i!==n[o]))return!1}return!0}function to(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const so=(e,t,s)=>e??t??s,sf=Gn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const r=Me(Sn),n=$e(()=>e.route||r.value),i=Me(Zi,0),o=$e(()=>{let u=lt(i);const{matched:c}=n.value;let h;for(;(h=c[u])&&!h.components;)u++;return u}),a=$e(()=>n.value.matched[o.value]);ur(Zi,$e(()=>o.value+1)),ur(Qh,a),ur(Sn,n);const l=as();return As(()=>[l.value,a.value,e.name],([u,c,h],[d,f,m])=>{c&&(c.instances[h]=u,f&&f!==c&&u&&u===d&&(c.leaveGuards.size||(c.leaveGuards=f.leaveGuards),c.updateGuards.size||(c.updateGuards=f.updateGuards))),u&&c&&(!f||!us(c,f)||!d)&&(c.enterCallbacks[h]||[]).forEach(v=>v(u))},{flush:"post"}),()=>{const u=n.value,c=e.name,h=a.value,d=h&&h.components[c];if(!d)return ro(s.default,{Component:d,route:u});const f=h.props[c],m=f?f===!0?u.params:typeof f=="function"?f(u):f:null,k=Da(d,Z({},m,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(h.instances[c]=null)},ref:l}));return ro(s.default,{Component:k,route:u})||k}}});function ro(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const tl=sf;function rf(e){const t=Hh(e.routes,e),s=e.parseQuery||Gh,r=e.stringifyQuery||Xi,n=e.history,i=ms(),o=ms(),a=ms(),l=tc(pt);let u=pt;rs&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Qr.bind(null,w=>""+w),h=Qr.bind(null,_h),d=Qr.bind(null,Vs);function f(w,$){let I,U;return Ya(w)?(I=t.getRecordMatcher(w),U=$):U=w,t.addRoute(U,I)}function m(w){const $=t.getRecordMatcher(w);$&&t.removeRoute($)}function v(){return t.getRoutes().map(w=>w.record)}function k(w){return!!t.getRecordMatcher(w)}function A(w,$){if($=Z({},$||l.value),typeof w=="string"){const _=Yr(s,w,$.path),y=t.resolve({path:_.path},$),E=n.createHref(_.fullPath);return Z(_,y,{params:d(y.params),hash:Vs(_.hash),redirectedFrom:void 0,href:E})}let I;if(w.path!=null)I=Z({},w,{path:Yr(s,w.path,$.path).path});else{const _=Z({},w.params);for(const y in _)_[y]==null&&delete _[y];I=Z({},w,{params:h(_)}),$.params=h($.params)}const U=t.resolve(I,$),se=w.hash||"";U.params=c(d(U.params));const p=yh(r,Z({},w,{hash:dh(se),path:U.path})),g=n.createHref(p);return Z({fullPath:p,hash:se,query:r===Xi?Jh(w.query):w.query||{}},U,{redirectedFrom:void 0,href:g})}function R(w){return typeof w=="string"?Yr(s,w,l.value.path):Z({},w)}function S(w,$){if(u!==w)return hs(8,{from:$,to:w})}function T(w){return q(w)}function L(w){return T(Z(R(w),{replace:!0}))}function V(w){const $=w.matched[w.matched.length-1];if($&&$.redirect){const{redirect:I}=$;let U=typeof I=="function"?I(w):I;return typeof U=="string"&&(U=U.includes("?")||U.includes("#")?U=R(U):{path:U},U.params={}),Z({query:w.query,hash:w.hash,params:U.path!=null?{}:w.params},U)}}function q(w,$){const I=u=A(w),U=l.value,se=w.state,p=w.force,g=w.replace===!0,_=V(I);if(_)return q(Z(R(_),{state:typeof _=="object"?Z({},se,_.state):se,force:p,replace:g}),$||I);const y=I;y.redirectedFrom=$;let E;return!p&&wh(r,U,I)&&(E=hs(16,{to:y,from:U}),We(U,U,!0,!1)),(E?Promise.resolve(E):ce(y,U)).catch(b=>tt(b)?tt(b,2)?b:dt(b):X(b,y,U)).then(b=>{if(b){if(tt(b,2))return q(Z({replace:g},R(b.to),{state:typeof b.to=="object"?Z({},se,b.to.state):se,force:p}),$||y)}else b=Ce(y,U,!0,g,se);return be(y,U,b),b})}function z(w,$){const I=S(w,$);return I?Promise.reject(I):Promise.resolve()}function G(w){const $=Ht.values().next().value;return $&&typeof $.runWithContext=="function"?$.runWithContext(w):w()}function ce(w,$){let I;const[U,se,p]=nf(w,$);I=Xr(U.reverse(),"beforeRouteLeave",w,$);for(const _ of U)_.leaveGuards.forEach(y=>{I.push(bt(y,w,$))});const g=z.bind(null,w,$);return I.push(g),Ue(I).then(()=>{I=[];for(const _ of i.list())I.push(bt(_,w,$));return I.push(g),Ue(I)}).then(()=>{I=Xr(se,"beforeRouteUpdate",w,$);for(const _ of se)_.updateGuards.forEach(y=>{I.push(bt(y,w,$))});return I.push(g),Ue(I)}).then(()=>{I=[];for(const _ of p)if(_.beforeEnter)if(He(_.beforeEnter))for(const y of _.beforeEnter)I.push(bt(y,w,$));else I.push(bt(_.beforeEnter,w,$));return I.push(g),Ue(I)}).then(()=>(w.matched.forEach(_=>_.enterCallbacks={}),I=Xr(p,"beforeRouteEnter",w,$,G),I.push(g),Ue(I))).then(()=>{I=[];for(const _ of o.list())I.push(bt(_,w,$));return I.push(g),Ue(I)}).catch(_=>tt(_,8)?_:Promise.reject(_))}function be(w,$,I){a.list().forEach(U=>G(()=>U(w,$,I)))}function Ce(w,$,I,U,se){const p=S(w,$);if(p)return p;const g=$===pt,_=rs?history.state:{};I&&(U||g?n.replace(w.fullPath,Z({scroll:g&&_&&_.scroll},se)):n.push(w.fullPath,se)),l.value=w,We(w,$,I,g),dt()}let Te;function Rt(){Te||(Te=n.listen((w,$,I)=>{if(!Xs.listening)return;const U=A(w),se=V(U);if(se){q(Z(se,{replace:!0,force:!0}),U).catch(js);return}u=U;const p=l.value;rs&&xh(Hi(p.fullPath,I.delta),Ur()),ce(U,p).catch(g=>tt(g,12)?g:tt(g,2)?(q(Z(R(g.to),{force:!0}),U).then(_=>{tt(_,20)&&!I.delta&&I.type===Ks.pop&&n.go(-1,!1)}).catch(js),Promise.reject()):(I.delta&&n.go(-I.delta,!1),X(g,U,p))).then(g=>{g=g||Ce(U,p,!1),g&&(I.delta&&!tt(g,8)?n.go(-I.delta,!1):I.type===Ks.pop&&tt(g,20)&&n.go(-1,!1)),be(U,p,g)}).catch(js)}))}let ft=ms(),ae=ms(),W;function X(w,$,I){dt(w);const U=ae.list();return U.length?U.forEach(se=>se(w,$,I)):console.error(w),Promise.reject(w)}function Ze(){return W&&l.value!==pt?Promise.resolve():new Promise((w,$)=>{ft.add([w,$])})}function dt(w){return W||(W=!w,Rt(),ft.list().forEach(([$,I])=>w?I(w):$()),ft.reset()),w}function We(w,$,I,U){const{scrollBehavior:se}=e;if(!rs||!se)return Promise.resolve();const p=!I&&Ah(Hi(w.fullPath,0))||(U||!I)&&history.state&&history.state.scroll||null;return Vn().then(()=>se(w,$,p)).then(g=>g&&Ph(g)).catch(g=>X(g,w,$))}const Oe=w=>n.go(w);let Ft;const Ht=new Set,Xs={currentRoute:l,listening:!0,addRoute:f,removeRoute:m,clearRoutes:t.clearRoutes,hasRoute:k,getRoutes:v,resolve:A,options:e,push:T,replace:L,go:Oe,back:()=>Oe(-1),forward:()=>Oe(1),beforeEach:i.add,beforeResolve:o.add,afterEach:a.add,onError:ae.add,isReady:Ze,install(w){const $=this;w.component("RouterLink",Zh),w.component("RouterView",tl),w.config.globalProperties.$router=$,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>lt(l)}),rs&&!Ft&&l.value===pt&&(Ft=!0,T(n.location).catch(se=>{}));const I={};for(const se in pt)Object.defineProperty(I,se,{get:()=>l.value[se],enumerable:!0});w.provide(Dr,$),w.provide(Zn,sa(I)),w.provide(Sn,l);const U=w.unmount;Ht.add(w),w.unmount=function(){Ht.delete(w),Ht.size<1&&(u=pt,Te&&Te(),Te=null,l.value=pt,Ft=!1,W=!1),U()}}};function Ue(w){return w.reduce(($,I)=>$.then(()=>G(I)),Promise.resolve())}return Xs}function nf(e,t){const s=[],r=[],n=[],i=Math.max(t.matched.length,e.matched.length);for(let o=0;o<i;o++){const a=t.matched[o];a&&(e.matched.find(u=>us(u,a))?r.push(a):s.push(a));const l=e.matched[o];l&&(t.matched.find(u=>us(u,l))||n.push(l))}return[s,r,n]}function Ep(){return Me(Dr)}function kp(e){return Me(Zn)}const of="modulepreload",af=function(e){return"/"+e},no={},fe=function(t,s,r){let n=Promise.resolve();if(s&&s.length>0){let l=function(u){return Promise.all(u.map(c=>Promise.resolve(c).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),a=o?.nonce||o?.getAttribute("nonce");n=l(s.map(u=>{if(u=af(u),u in no)return;no[u]=!0;const c=u.endsWith(".css"),h=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${h}`))return;const d=document.createElement("link");if(d.rel=c?"stylesheet":of,c||(d.as="script"),d.crossOrigin="",d.href=u,a&&d.setAttribute("nonce",a),document.head.appendChild(d),c)return new Promise((f,m)=>{d.addEventListener("load",f),d.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return n.then(o=>{for(const a of o||[])a.status==="rejected"&&i(a.reason);return t().catch(i)})},lf=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...s)=>fe(async()=>{const{default:r}=await Promise.resolve().then(()=>ds);return{default:r}},void 0).then(({default:r})=>r(...s)):t=fetch,(...s)=>t(...s)};class ei extends Error{constructor(t,s="FunctionsError",r){super(t),this.name=s,this.context=r}}class cf extends ei{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class io extends ei{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class oo extends ei{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var En;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(En||(En={}));var uf=function(e,t,s,r){function n(i){return i instanceof s?i:new s(function(o){o(i)})}return new(s||(s=Promise))(function(i,o){function a(c){try{u(r.next(c))}catch(h){o(h)}}function l(c){try{u(r.throw(c))}catch(h){o(h)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class hf{constructor(t,{headers:s={},customFetch:r,region:n=En.Any}={}){this.url=t,this.headers=s,this.region=n,this.fetch=lf(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,s={}){var r;return uf(this,void 0,void 0,function*(){try{const{headers:n,method:i,body:o}=s;let a={},{region:l}=s;l||(l=this.region);const u=new URL(`${this.url}/${t}`);l&&l!=="any"&&(a["x-region"]=l,u.searchParams.set("forceFunctionRegion",l));let c;o&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",c=o):typeof o=="string"?(a["Content-Type"]="text/plain",c=o):typeof FormData<"u"&&o instanceof FormData?c=o:(a["Content-Type"]="application/json",c=JSON.stringify(o)));const h=yield this.fetch(u.toString(),{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),n),body:c}).catch(v=>{throw new cf(v)}),d=h.headers.get("x-relay-error");if(d&&d==="true")throw new io(h);if(!h.ok)throw new oo(h);let f=((r=h.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),m;return f==="application/json"?m=yield h.json():f==="application/octet-stream"?m=yield h.blob():f==="text/event-stream"?m=h:f==="multipart/form-data"?m=yield h.formData():m=yield h.text(),{data:m,error:null,response:h}}catch(n){return{data:null,error:n,response:n instanceof oo||n instanceof io?n.context:void 0}}})}}function ff(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function df(e){if(Object.prototype.hasOwnProperty.call(e,"__esModule"))return e;var t=e.default;if(typeof t=="function"){var s=function r(){var n=!1;try{n=this instanceof r}catch{}return n?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};s.prototype=t.prototype}else s={};return Object.defineProperty(s,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(s,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}),s}var de={},Kt={},zt={},Gt={},Jt={},Qt={},pf=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},fs=pf();const gf=fs.fetch,sl=fs.fetch.bind(fs),rl=fs.Headers,_f=fs.Request,mf=fs.Response,ds=Object.freeze(Object.defineProperty({__proto__:null,Headers:rl,Request:_f,Response:mf,default:sl,fetch:gf},Symbol.toStringTag,{value:"Module"})),vf=df(ds);var sr={},ao;function nl(){if(ao)return sr;ao=1,Object.defineProperty(sr,"__esModule",{value:!0});class e extends Error{constructor(s){super(s.message),this.name="PostgrestError",this.details=s.details,this.hint=s.hint,this.code=s.code}}return sr.default=e,sr}var lo;function il(){if(lo)return Qt;lo=1;var e=Qt&&Qt.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(Qt,"__esModule",{value:!0});const t=e(vf),s=e(nl());class r{constructor(i){this.shouldThrowOnError=!1,this.method=i.method,this.url=i.url,this.headers=i.headers,this.schema=i.schema,this.body=i.body,this.shouldThrowOnError=i.shouldThrowOnError,this.signal=i.signal,this.isMaybeSingle=i.isMaybeSingle,i.fetch?this.fetch=i.fetch:typeof fetch>"u"?this.fetch=t.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(i,o){return this.headers=Object.assign({},this.headers),this.headers[i]=o,this}then(i,o){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const a=this.fetch;let l=a(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async u=>{var c,h,d;let f=null,m=null,v=null,k=u.status,A=u.statusText;if(u.ok){if(this.method!=="HEAD"){const L=await u.text();L===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?m=L:m=JSON.parse(L))}const S=(c=this.headers.Prefer)===null||c===void 0?void 0:c.match(/count=(exact|planned|estimated)/),T=(h=u.headers.get("content-range"))===null||h===void 0?void 0:h.split("/");S&&T&&T.length>1&&(v=parseInt(T[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(m)&&(m.length>1?(f={code:"PGRST116",details:`Results contain ${m.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},m=null,v=null,k=406,A="Not Acceptable"):m.length===1?m=m[0]:m=null)}else{const S=await u.text();try{f=JSON.parse(S),Array.isArray(f)&&u.status===404&&(m=[],f=null,k=200,A="OK")}catch{u.status===404&&S===""?(k=204,A="No Content"):f={message:S}}if(f&&this.isMaybeSingle&&(!((d=f?.details)===null||d===void 0)&&d.includes("0 rows"))&&(f=null,k=200,A="OK"),f&&this.shouldThrowOnError)throw new s.default(f)}return{error:f,data:m,count:v,status:k,statusText:A}});return this.shouldThrowOnError||(l=l.catch(u=>{var c,h,d;return{error:{message:`${(c=u?.name)!==null&&c!==void 0?c:"FetchError"}: ${u?.message}`,details:`${(h=u?.stack)!==null&&h!==void 0?h:""}`,hint:"",code:`${(d=u?.code)!==null&&d!==void 0?d:""}`},data:null,count:null,status:0,statusText:""}})),l.then(i,o)}returns(){return this}overrideTypes(){return this}}return Qt.default=r,Qt}var co;function ol(){if(co)return Jt;co=1;var e=Jt&&Jt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Jt,"__esModule",{value:!0});const t=e(il());class s extends t.default{select(n){let i=!1;const o=(n??"*").split("").map(a=>/\s/.test(a)&&!i?"":(a==='"'&&(i=!i),a)).join("");return this.url.searchParams.set("select",o),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(n,{ascending:i=!0,nullsFirst:o,foreignTable:a,referencedTable:l=a}={}){const u=l?`${l}.order`:"order",c=this.url.searchParams.get(u);return this.url.searchParams.set(u,`${c?`${c},`:""}${n}.${i?"asc":"desc"}${o===void 0?"":o?".nullsfirst":".nullslast"}`),this}limit(n,{foreignTable:i,referencedTable:o=i}={}){const a=typeof o>"u"?"limit":`${o}.limit`;return this.url.searchParams.set(a,`${n}`),this}range(n,i,{foreignTable:o,referencedTable:a=o}={}){const l=typeof a>"u"?"offset":`${a}.offset`,u=typeof a>"u"?"limit":`${a}.limit`;return this.url.searchParams.set(l,`${n}`),this.url.searchParams.set(u,`${i-n+1}`),this}abortSignal(n){return this.signal=n,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:n=!1,verbose:i=!1,settings:o=!1,buffers:a=!1,wal:l=!1,format:u="text"}={}){var c;const h=[n?"analyze":null,i?"verbose":null,o?"settings":null,a?"buffers":null,l?"wal":null].filter(Boolean).join("|"),d=(c=this.headers.Accept)!==null&&c!==void 0?c:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${u}; for="${d}"; options=${h};`,u==="json"?this:this}rollback(){var n;return((n=this.headers.Prefer)!==null&&n!==void 0?n:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return Jt.default=s,Jt}var uo;function ti(){if(uo)return Gt;uo=1;var e=Gt&&Gt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Gt,"__esModule",{value:!0});const t=e(ol());class s extends t.default{eq(n,i){return this.url.searchParams.append(n,`eq.${i}`),this}neq(n,i){return this.url.searchParams.append(n,`neq.${i}`),this}gt(n,i){return this.url.searchParams.append(n,`gt.${i}`),this}gte(n,i){return this.url.searchParams.append(n,`gte.${i}`),this}lt(n,i){return this.url.searchParams.append(n,`lt.${i}`),this}lte(n,i){return this.url.searchParams.append(n,`lte.${i}`),this}like(n,i){return this.url.searchParams.append(n,`like.${i}`),this}likeAllOf(n,i){return this.url.searchParams.append(n,`like(all).{${i.join(",")}}`),this}likeAnyOf(n,i){return this.url.searchParams.append(n,`like(any).{${i.join(",")}}`),this}ilike(n,i){return this.url.searchParams.append(n,`ilike.${i}`),this}ilikeAllOf(n,i){return this.url.searchParams.append(n,`ilike(all).{${i.join(",")}}`),this}ilikeAnyOf(n,i){return this.url.searchParams.append(n,`ilike(any).{${i.join(",")}}`),this}is(n,i){return this.url.searchParams.append(n,`is.${i}`),this}in(n,i){const o=Array.from(new Set(i)).map(a=>typeof a=="string"&&new RegExp("[,()]").test(a)?`"${a}"`:`${a}`).join(",");return this.url.searchParams.append(n,`in.(${o})`),this}contains(n,i){return typeof i=="string"?this.url.searchParams.append(n,`cs.${i}`):Array.isArray(i)?this.url.searchParams.append(n,`cs.{${i.join(",")}}`):this.url.searchParams.append(n,`cs.${JSON.stringify(i)}`),this}containedBy(n,i){return typeof i=="string"?this.url.searchParams.append(n,`cd.${i}`):Array.isArray(i)?this.url.searchParams.append(n,`cd.{${i.join(",")}}`):this.url.searchParams.append(n,`cd.${JSON.stringify(i)}`),this}rangeGt(n,i){return this.url.searchParams.append(n,`sr.${i}`),this}rangeGte(n,i){return this.url.searchParams.append(n,`nxl.${i}`),this}rangeLt(n,i){return this.url.searchParams.append(n,`sl.${i}`),this}rangeLte(n,i){return this.url.searchParams.append(n,`nxr.${i}`),this}rangeAdjacent(n,i){return this.url.searchParams.append(n,`adj.${i}`),this}overlaps(n,i){return typeof i=="string"?this.url.searchParams.append(n,`ov.${i}`):this.url.searchParams.append(n,`ov.{${i.join(",")}}`),this}textSearch(n,i,{config:o,type:a}={}){let l="";a==="plain"?l="pl":a==="phrase"?l="ph":a==="websearch"&&(l="w");const u=o===void 0?"":`(${o})`;return this.url.searchParams.append(n,`${l}fts${u}.${i}`),this}match(n){return Object.entries(n).forEach(([i,o])=>{this.url.searchParams.append(i,`eq.${o}`)}),this}not(n,i,o){return this.url.searchParams.append(n,`not.${i}.${o}`),this}or(n,{foreignTable:i,referencedTable:o=i}={}){const a=o?`${o}.or`:"or";return this.url.searchParams.append(a,`(${n})`),this}filter(n,i,o){return this.url.searchParams.append(n,`${i}.${o}`),this}}return Gt.default=s,Gt}var ho;function al(){if(ho)return zt;ho=1;var e=zt&&zt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(zt,"__esModule",{value:!0});const t=e(ti());class s{constructor(n,{headers:i={},schema:o,fetch:a}){this.url=n,this.headers=i,this.schema=o,this.fetch=a}select(n,{head:i=!1,count:o}={}){const a=i?"HEAD":"GET";let l=!1;const u=(n??"*").split("").map(c=>/\s/.test(c)&&!l?"":(c==='"'&&(l=!l),c)).join("");return this.url.searchParams.set("select",u),o&&(this.headers.Prefer=`count=${o}`),new t.default({method:a,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(n,{count:i,defaultToNull:o=!0}={}){const a="POST",l=[];if(this.headers.Prefer&&l.push(this.headers.Prefer),i&&l.push(`count=${i}`),o||l.push("missing=default"),this.headers.Prefer=l.join(","),Array.isArray(n)){const u=n.reduce((c,h)=>c.concat(Object.keys(h)),[]);if(u.length>0){const c=[...new Set(u)].map(h=>`"${h}"`);this.url.searchParams.set("columns",c.join(","))}}return new t.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}upsert(n,{onConflict:i,ignoreDuplicates:o=!1,count:a,defaultToNull:l=!0}={}){const u="POST",c=[`resolution=${o?"ignore":"merge"}-duplicates`];if(i!==void 0&&this.url.searchParams.set("on_conflict",i),this.headers.Prefer&&c.push(this.headers.Prefer),a&&c.push(`count=${a}`),l||c.push("missing=default"),this.headers.Prefer=c.join(","),Array.isArray(n)){const h=n.reduce((d,f)=>d.concat(Object.keys(f)),[]);if(h.length>0){const d=[...new Set(h)].map(f=>`"${f}"`);this.url.searchParams.set("columns",d.join(","))}}return new t.default({method:u,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}update(n,{count:i}={}){const o="PATCH",a=[];return this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),this.headers.Prefer=a.join(","),new t.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}delete({count:n}={}){const i="DELETE",o=[];return n&&o.push(`count=${n}`),this.headers.Prefer&&o.unshift(this.headers.Prefer),this.headers.Prefer=o.join(","),new t.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return zt.default=s,zt}var vs={},ys={},fo;function yf(){return fo||(fo=1,Object.defineProperty(ys,"__esModule",{value:!0}),ys.version=void 0,ys.version="0.0.0-automated"),ys}var po;function wf(){if(po)return vs;po=1,Object.defineProperty(vs,"__esModule",{value:!0}),vs.DEFAULT_HEADERS=void 0;const e=yf();return vs.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${e.version}`},vs}var go;function bf(){if(go)return Kt;go=1;var e=Kt&&Kt.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(Kt,"__esModule",{value:!0});const t=e(al()),s=e(ti()),r=wf();class n{constructor(o,{headers:a={},schema:l,fetch:u}={}){this.url=o,this.headers=Object.assign(Object.assign({},r.DEFAULT_HEADERS),a),this.schemaName=l,this.fetch=u}from(o){const a=new URL(`${this.url}/${o}`);return new t.default(a,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(o){return new n(this.url,{headers:this.headers,schema:o,fetch:this.fetch})}rpc(o,a={},{head:l=!1,get:u=!1,count:c}={}){let h;const d=new URL(`${this.url}/rpc/${o}`);let f;l||u?(h=l?"HEAD":"GET",Object.entries(a).filter(([v,k])=>k!==void 0).map(([v,k])=>[v,Array.isArray(k)?`{${k.join(",")}}`:`${k}`]).forEach(([v,k])=>{d.searchParams.append(v,k)})):(h="POST",f=a);const m=Object.assign({},this.headers);return c&&(m.Prefer=`count=${c}`),new s.default({method:h,url:d,headers:m,schema:this.schemaName,body:f,fetch:this.fetch,allowEmpty:!1})}}return Kt.default=n,Kt}var _o;function Sf(){if(_o)return de;_o=1;var e=de&&de.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(de,"__esModule",{value:!0}),de.PostgrestError=de.PostgrestBuilder=de.PostgrestTransformBuilder=de.PostgrestFilterBuilder=de.PostgrestQueryBuilder=de.PostgrestClient=void 0;const t=e(bf());de.PostgrestClient=t.default;const s=e(al());de.PostgrestQueryBuilder=s.default;const r=e(ti());de.PostgrestFilterBuilder=r.default;const n=e(ol());de.PostgrestTransformBuilder=n.default;const i=e(il());de.PostgrestBuilder=i.default;const o=e(nl());return de.PostgrestError=o.default,de.default={PostgrestClient:t.default,PostgrestQueryBuilder:s.default,PostgrestFilterBuilder:r.default,PostgrestTransformBuilder:n.default,PostgrestBuilder:i.default,PostgrestError:o.default},de}var Ef=Sf();const kf=ff(Ef),{PostgrestClient:Tf,PostgrestQueryBuilder:Tp,PostgrestFilterBuilder:Op,PostgrestTransformBuilder:Pp,PostgrestBuilder:xp,PostgrestError:Ap}=kf;function Of(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const Pf=Of(),xf="2.11.15",Af=`realtime-js/${xf}`,Rf="1.0.0",ll=1e4,Cf=1e3;var Ls;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Ls||(Ls={}));var me;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(me||(me={}));var qe;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(qe||(qe={}));var kn;(function(e){e.websocket="websocket"})(kn||(kn={}));var Dt;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(Dt||(Dt={}));class If{constructor(){this.HEADER_LENGTH=1}decode(t,s){return t.constructor===ArrayBuffer?s(this._binaryDecode(t)):s(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const s=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,s,r)}_decodeBroadcast(t,s,r){const n=s.getUint8(1),i=s.getUint8(2);let o=this.HEADER_LENGTH+2;const a=r.decode(t.slice(o,o+n));o=o+n;const l=r.decode(t.slice(o,o+i));o=o+i;const u=JSON.parse(r.decode(t.slice(o,t.byteLength)));return{ref:null,topic:a,event:l,payload:u}}}class cl{constructor(t,s){this.callback=t,this.timerCalc=s,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=s}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var ie;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(ie||(ie={}));const mo=(e,t,s={})=>{var r;const n=(r=s.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((i,o)=>(i[o]=jf(o,e,t,n),i),{})},jf=(e,t,s,r)=>{const n=t.find(a=>a.name===e),i=n?.type,o=s[e];return i&&!r.includes(i)?ul(i,o):Tn(o)},ul=(e,t)=>{if(e.charAt(0)==="_"){const s=e.slice(1,e.length);return Df(t,s)}switch(e){case ie.bool:return $f(t);case ie.float4:case ie.float8:case ie.int2:case ie.int4:case ie.int8:case ie.numeric:case ie.oid:return Lf(t);case ie.json:case ie.jsonb:return Uf(t);case ie.timestamp:return Mf(t);case ie.abstime:case ie.date:case ie.daterange:case ie.int4range:case ie.int8range:case ie.money:case ie.reltime:case ie.text:case ie.time:case ie.timestamptz:case ie.timetz:case ie.tsrange:case ie.tstzrange:return Tn(t);default:return Tn(t)}},Tn=e=>e,$f=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Lf=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Uf=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},Df=(e,t)=>{if(typeof e!="string")return e;const s=e.length-1,r=e[s];if(e[0]==="{"&&r==="}"){let i;const o=e.slice(1,s);try{i=JSON.parse("["+o+"]")}catch{i=o?o.split(","):[]}return i.map(a=>ul(t,a))}return e},Mf=e=>typeof e=="string"?e.replace(" ","T"):e,hl=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Zr{constructor(t,s,r={},n=ll){this.channel=t,this.event=s,this.payload=r,this.timeout=n,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,s){var r;return this._hasReceived(t)&&s((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:s}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=s=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=s,this._matchReceive(s)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,s){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:s})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:s}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(s))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var vo;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(vo||(vo={}));class Us{constructor(t,s){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=s?.events||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},n=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Us.syncState(this.state,n,i,o),this.pendingDiffs.forEach(l=>{this.state=Us.syncDiff(this.state,l,i,o)}),this.pendingDiffs=[],a()}),this.channel._on(r.diff,{},n=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(n):(this.state=Us.syncDiff(this.state,n,i,o),a())}),this.onJoin((n,i,o)=>{this.channel._trigger("presence",{event:"join",key:n,currentPresences:i,newPresences:o})}),this.onLeave((n,i,o)=>{this.channel._trigger("presence",{event:"leave",key:n,currentPresences:i,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,s,r,n){const i=this.cloneDeep(t),o=this.transformState(s),a={},l={};return this.map(i,(u,c)=>{o[u]||(l[u]=c)}),this.map(o,(u,c)=>{const h=i[u];if(h){const d=c.map(k=>k.presence_ref),f=h.map(k=>k.presence_ref),m=c.filter(k=>f.indexOf(k.presence_ref)<0),v=h.filter(k=>d.indexOf(k.presence_ref)<0);m.length>0&&(a[u]=m),v.length>0&&(l[u]=v)}else a[u]=c}),this.syncDiff(i,{joins:a,leaves:l},r,n)}static syncDiff(t,s,r,n){const{joins:i,leaves:o}={joins:this.transformState(s.joins),leaves:this.transformState(s.leaves)};return r||(r=()=>{}),n||(n=()=>{}),this.map(i,(a,l)=>{var u;const c=(u=t[a])!==null&&u!==void 0?u:[];if(t[a]=this.cloneDeep(l),c.length>0){const h=t[a].map(f=>f.presence_ref),d=c.filter(f=>h.indexOf(f.presence_ref)<0);t[a].unshift(...d)}r(a,c,l)}),this.map(o,(a,l)=>{let u=t[a];if(!u)return;const c=l.map(h=>h.presence_ref);u=u.filter(h=>c.indexOf(h.presence_ref)<0),t[a]=u,n(a,u,l),u.length===0&&delete t[a]}),t}static map(t,s){return Object.getOwnPropertyNames(t).map(r=>s(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((s,r)=>{const n=t[r];return"metas"in n?s[r]=n.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):s[r]=n,s},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var yo;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(yo||(yo={}));var wo;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(wo||(wo={}));var nt;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(nt||(nt={}));class si{constructor(t,s={config:{}},r){this.topic=t,this.params=s,this.socket=r,this.bindings={},this.state=me.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},s.config),this.timeout=this.socket.timeout,this.joinPush=new Zr(this,qe.join,this.params,this.timeout),this.rejoinTimer=new cl(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=me.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(n=>n.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=me.closed,this.socket._remove(this)}),this._onError(n=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,n),this.state=me.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=me.errored,this.rejoinTimer.scheduleTimeout())}),this._on(qe.reply,{},(n,i)=>{this._trigger(this._replyEventName(i),n)}),this.presence=new Us(this),this.broadcastEndpointURL=hl(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,s=this.timeout){var r,n;if(this.socket.isConnected()||this.socket.connect(),this.state==me.closed){const{config:{broadcast:i,presence:o,private:a}}=this.params;this._onError(c=>t?.(nt.CHANNEL_ERROR,c)),this._onClose(()=>t?.(nt.CLOSED));const l={},u={broadcast:i,presence:o,postgres_changes:(n=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(c=>c.filter))!==null&&n!==void 0?n:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},l)),this.joinedOnce=!0,this._rejoin(s),this.joinPush.receive("ok",async({postgres_changes:c})=>{var h;if(this.socket.setAuth(),c===void 0){t?.(nt.SUBSCRIBED);return}else{const d=this.bindings.postgres_changes,f=(h=d?.length)!==null&&h!==void 0?h:0,m=[];for(let v=0;v<f;v++){const k=d[v],{filter:{event:A,schema:R,table:S,filter:T}}=k,L=c&&c[v];if(L&&L.event===A&&L.schema===R&&L.table===S&&L.filter===T)m.push(Object.assign(Object.assign({},k),{id:L.id}));else{this.unsubscribe(),this.state=me.errored,t?.(nt.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=m,t&&t(nt.SUBSCRIBED);return}}).receive("error",c=>{this.state=me.errored,t?.(nt.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{t?.(nt.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,s={}){return await this.send({type:"presence",event:"track",payload:t},s.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,s,r){return this._on(t,s,r)}async send(t,s={}){var r,n;if(!this._canPush()&&t.type==="broadcast"){const{event:i,payload:o}=t,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(r=s.timeout)!==null&&r!==void 0?r:this.timeout);return await((n=u.body)===null||n===void 0?void 0:n.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(i=>{var o,a,l;const u=this._push(t.type,t,s.timeout||this.timeout);t.type==="broadcast"&&!(!((l=(a=(o=this.params)===null||o===void 0?void 0:o.config)===null||a===void 0?void 0:a.broadcast)===null||l===void 0)&&l.ack)&&i("ok"),u.receive("ok",()=>i("ok")),u.receive("error",()=>i("error")),u.receive("timeout",()=>i("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=me.leaving;const s=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(qe.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(n=>{r=new Zr(this,qe.leave,{},t),r.receive("ok",()=>{s(),n("ok")}).receive("timeout",()=>{s(),n("timed out")}).receive("error",()=>{n("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{r?.destroy()})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(t,s,r){const n=new AbortController,i=setTimeout(()=>n.abort(),r),o=await this.socket.fetch(t,Object.assign(Object.assign({},s),{signal:n.signal}));return clearTimeout(i),o}_push(t,s,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let n=new Zr(this,t,s,r);return this._canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}_onMessage(t,s,r){return s}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,s,r){var n,i;const o=t.toLocaleLowerCase(),{close:a,error:l,leave:u,join:c}=qe;if(r&&[a,l,u,c].indexOf(o)>=0&&r!==this._joinRef())return;let d=this._onMessage(o,s,r);if(s&&!d)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(n=this.bindings.postgres_changes)===null||n===void 0||n.filter(f=>{var m,v,k;return((m=f.filter)===null||m===void 0?void 0:m.event)==="*"||((k=(v=f.filter)===null||v===void 0?void 0:v.event)===null||k===void 0?void 0:k.toLocaleLowerCase())===o}).map(f=>f.callback(d,r)):(i=this.bindings[o])===null||i===void 0||i.filter(f=>{var m,v,k,A,R,S;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in f){const T=f.id,L=(m=f.filter)===null||m===void 0?void 0:m.event;return T&&((v=s.ids)===null||v===void 0?void 0:v.includes(T))&&(L==="*"||L?.toLocaleLowerCase()===((k=s.data)===null||k===void 0?void 0:k.type.toLocaleLowerCase()))}else{const T=(R=(A=f?.filter)===null||A===void 0?void 0:A.event)===null||R===void 0?void 0:R.toLocaleLowerCase();return T==="*"||T===((S=s?.event)===null||S===void 0?void 0:S.toLocaleLowerCase())}else return f.type.toLocaleLowerCase()===o}).map(f=>{if(typeof d=="object"&&"ids"in d){const m=d.data,{schema:v,table:k,commit_timestamp:A,type:R,errors:S}=m;d=Object.assign(Object.assign({},{schema:v,table:k,commit_timestamp:A,eventType:R,new:{},old:{},errors:S}),this._getPayloadRecords(m))}f.callback(d,r)})}_isClosed(){return this.state===me.closed}_isJoined(){return this.state===me.joined}_isJoining(){return this.state===me.joining}_isLeaving(){return this.state===me.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,s,r){const n=t.toLocaleLowerCase(),i={type:n,filter:s,callback:r};return this.bindings[n]?this.bindings[n].push(i):this.bindings[n]=[i],this}_off(t,s){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(n=>{var i;return!(((i=n.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===r&&si.isEqual(n.filter,s))}),this}static isEqual(t,s){if(Object.keys(t).length!==Object.keys(s).length)return!1;for(const r in t)if(t[r]!==s[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(qe.close,{},t)}_onError(t){this._on(qe.error,{},s=>t(s))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=me.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const s={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(s.new=mo(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(s.old=mo(t.columns,t.old_record)),s}}const bo=()=>{},Nf=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Bf{constructor(t,s){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=ll,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=bo,this.ref=0,this.logger=bo,this.conn=null,this.sendBuffer=[],this.serializer=new If,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let o;return i?o=i:typeof fetch>"u"?o=(...a)=>fe(async()=>{const{default:l}=await Promise.resolve().then(()=>ds);return{default:l}},void 0).then(({default:l})=>l(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${t}/${kn.websocket}`,this.httpEndpoint=hl(t),s?.transport?this.transport=s.transport:this.transport=null,s?.params&&(this.params=s.params),s?.timeout&&(this.timeout=s.timeout),s?.logger&&(this.logger=s.logger),(s?.logLevel||s?.log_level)&&(this.logLevel=s.logLevel||s.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),s?.heartbeatIntervalMs&&(this.heartbeatIntervalMs=s.heartbeatIntervalMs);const n=(r=s?.params)===null||r===void 0?void 0:r.apikey;if(n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=s?.reconnectAfterMs?s.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=s?.encode?s.encode:(i,o)=>o(JSON.stringify(i)),this.decode=s?.decode?s.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new cl(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(s?.fetch),s?.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=s?.worker||!1,this.workerUrl=s?.workerUrl}this.accessToken=s?.accessToken||null}connect(){if(!this.conn){if(this.transport||(this.transport=Pf),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Rf}))}disconnect(t,s){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,s??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(t){const s=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),s}async removeAllChannels(){const t=await Promise.all(this.channels.map(s=>s.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,s,r){this.logger(t,s,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Ls.connecting:return Dt.Connecting;case Ls.open:return Dt.Open;case Ls.closing:return Dt.Closing;default:return Dt.Closed}}isConnected(){return this.connectionState()===Dt.Open}channel(t,s={config:{}}){const r=`realtime:${t}`,n=this.getChannels().find(i=>i.topic===r);if(n)return n;{const i=new si(`realtime:${t}`,s,this);return this.channels.push(i),i}}push(t){const{topic:s,event:r,payload:n,ref:i}=t,o=()=>{this.encode(t,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${s} ${r} (${i})`,n),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(t=null){let s=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=s&&(this.accessTokenValue=s,this.channels.forEach(r=>{const n={access_token:s,version:Af};s&&r.updateJoinPayload(n),r.joinedOnce&&r._isJoined()&&r._push(qe.access_token,{access_token:s})}))}async sendHeartbeat(){var t;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(t=this.conn)===null||t===void 0||t.close(Cf,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let s=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));s&&(this.log("transport",`leaving duplicate topic "${t}"`),s.unsubscribe())}_remove(t){this.channels=this.channels.filter(s=>s.topic!==t.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,s=>{let{topic:r,event:n,payload:i,ref:o}=s;r==="phoenix"&&n==="phx_reply"&&this.heartbeatCallback(s.payload.status=="ok"?"ok":"error"),o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${r} ${n} ${o&&"("+o+")"||""}`,i),Array.from(this.channels).filter(a=>a._isMember(r)).forEach(a=>a._trigger(n,i,o)),this.stateChangeCallbacks.message.forEach(a=>a(s))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(t=>t())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=s=>{this.log("worker","worker error",s.message),this.workerRef.terminate()},this.workerRef.onmessage=s=>{s.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(s=>s(t))}_onConnError(t){this.log("transport",`${t}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(s=>s(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(qe.error))}_appendParams(t,s){if(Object.keys(s).length===0)return t;const r=t.match(/\?/)?"&":"?",n=new URLSearchParams(s);return`${t}${r}${n}`}_workerObjectUrl(t){let s;if(t)s=t;else{const r=new Blob([Nf],{type:"application/javascript"});s=URL.createObjectURL(r)}return s}}class ri extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function pe(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class qf extends ri{constructor(t,s){super(t),this.name="StorageApiError",this.status=s}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class On extends ri{constructor(t,s){super(t),this.name="StorageUnknownError",this.originalError=s}}var Ff=function(e,t,s,r){function n(i){return i instanceof s?i:new s(function(o){o(i)})}return new(s||(s=Promise))(function(i,o){function a(c){try{u(r.next(c))}catch(h){o(h)}}function l(c){try{u(r.throw(c))}catch(h){o(h)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const fl=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...s)=>fe(async()=>{const{default:r}=await Promise.resolve().then(()=>ds);return{default:r}},void 0).then(({default:r})=>r(...s)):t=fetch,(...s)=>t(...s)},Hf=()=>Ff(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield fe(()=>Promise.resolve().then(()=>ds),void 0)).Response:Response}),Pn=e=>{if(Array.isArray(e))return e.map(s=>Pn(s));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([s,r])=>{const n=s.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));t[n]=Pn(r)}),t};var qt=function(e,t,s,r){function n(i){return i instanceof s?i:new s(function(o){o(i)})}return new(s||(s=Promise))(function(i,o){function a(c){try{u(r.next(c))}catch(h){o(h)}}function l(c){try{u(r.throw(c))}catch(h){o(h)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const en=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Wf=(e,t,s)=>qt(void 0,void 0,void 0,function*(){const r=yield Hf();e instanceof r&&!s?.noResolveJson?e.json().then(n=>{t(new qf(en(n),e.status||500))}).catch(n=>{t(new On(en(n),n))}):t(new On(en(e),e))}),Vf=(e,t,s,r)=>{const n={method:e,headers:t?.headers||{}};return e==="GET"?n:(n.headers=Object.assign({"Content-Type":"application/json"},t?.headers),r&&(n.body=JSON.stringify(r)),Object.assign(Object.assign({},n),s))};function Ys(e,t,s,r,n,i){return qt(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(s,Vf(t,r,n,i)).then(l=>{if(!l.ok)throw l;return r?.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>Wf(l,a,r))})})}function Sr(e,t,s,r){return qt(this,void 0,void 0,function*(){return Ys(e,"GET",t,s,r)})}function St(e,t,s,r,n){return qt(this,void 0,void 0,function*(){return Ys(e,"POST",t,r,n,s)})}function Kf(e,t,s,r,n){return qt(this,void 0,void 0,function*(){return Ys(e,"PUT",t,r,n,s)})}function zf(e,t,s,r){return qt(this,void 0,void 0,function*(){return Ys(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),r)})}function dl(e,t,s,r,n){return qt(this,void 0,void 0,function*(){return Ys(e,"DELETE",t,r,n,s)})}var Ae=function(e,t,s,r){function n(i){return i instanceof s?i:new s(function(o){o(i)})}return new(s||(s=Promise))(function(i,o){function a(c){try{u(r.next(c))}catch(h){o(h)}}function l(c){try{u(r.throw(c))}catch(h){o(h)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Gf={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},So={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Jf{constructor(t,s={},r,n){this.url=t,this.headers=s,this.bucketId=r,this.fetch=fl(n)}uploadOrUpdate(t,s,r,n){return Ae(this,void 0,void 0,function*(){try{let i;const o=Object.assign(Object.assign({},So),n);let a=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)});const l=o.metadata;typeof Blob<"u"&&r instanceof Blob?(i=new FormData,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l)),i.append("",r)):typeof FormData<"u"&&r instanceof FormData?(i=r,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l))):(i=r,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),n?.headers&&(a=Object.assign(Object.assign({},a),n.headers));const u=this._removeEmptyFolders(s),c=this._getFinalPath(u),h=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:i,headers:a},o?.duplex?{duplex:o.duplex}:{})),d=yield h.json();return h.ok?{data:{path:u,id:d.Id,fullPath:d.Key},error:null}:{data:null,error:d}}catch(i){if(pe(i))return{data:null,error:i};throw i}})}upload(t,s,r){return Ae(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,s,r)})}uploadToSignedUrl(t,s,r,n){return Ae(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(t),o=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",s);try{let l;const u=Object.assign({upsert:So.upsert},n),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&r instanceof Blob?(l=new FormData,l.append("cacheControl",u.cacheControl),l.append("",r)):typeof FormData<"u"&&r instanceof FormData?(l=r,l.append("cacheControl",u.cacheControl)):(l=r,c["cache-control"]=`max-age=${u.cacheControl}`,c["content-type"]=u.contentType);const h=yield this.fetch(a.toString(),{method:"PUT",body:l,headers:c}),d=yield h.json();return h.ok?{data:{path:i,fullPath:d.Key},error:null}:{data:null,error:d}}catch(l){if(pe(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,s){return Ae(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const n=Object.assign({},this.headers);s?.upsert&&(n["x-upsert"]="true");const i=yield St(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:n}),o=new URL(this.url+i.url),a=o.searchParams.get("token");if(!a)throw new ri("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:a},error:null}}catch(r){if(pe(r))return{data:null,error:r};throw r}})}update(t,s,r){return Ae(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,s,r)})}move(t,s,r){return Ae(this,void 0,void 0,function*(){try{return{data:yield St(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:s,destinationBucket:r?.destinationBucket},{headers:this.headers}),error:null}}catch(n){if(pe(n))return{data:null,error:n};throw n}})}copy(t,s,r){return Ae(this,void 0,void 0,function*(){try{return{data:{path:(yield St(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:s,destinationBucket:r?.destinationBucket},{headers:this.headers})).Key},error:null}}catch(n){if(pe(n))return{data:null,error:n};throw n}})}createSignedUrl(t,s,r){return Ae(this,void 0,void 0,function*(){try{let n=this._getFinalPath(t),i=yield St(this.fetch,`${this.url}/object/sign/${n}`,Object.assign({expiresIn:s},r?.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r?.download?`&download=${r.download===!0?"":r.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${o}`)},{data:i,error:null}}catch(n){if(pe(n))return{data:null,error:n};throw n}})}createSignedUrls(t,s,r){return Ae(this,void 0,void 0,function*(){try{const n=yield St(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:s,paths:t},{headers:this.headers}),i=r?.download?`&download=${r.download===!0?"":r.download}`:"";return{data:n.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${i}`):null})),error:null}}catch(n){if(pe(n))return{data:null,error:n};throw n}})}download(t,s){return Ae(this,void 0,void 0,function*(){const n=typeof s?.transform<"u"?"render/image/authenticated":"object",i=this.transformOptsToQueryString(s?.transform||{}),o=i?`?${i}`:"";try{const a=this._getFinalPath(t);return{data:yield(yield Sr(this.fetch,`${this.url}/${n}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(pe(a))return{data:null,error:a};throw a}})}info(t){return Ae(this,void 0,void 0,function*(){const s=this._getFinalPath(t);try{const r=yield Sr(this.fetch,`${this.url}/object/info/${s}`,{headers:this.headers});return{data:Pn(r),error:null}}catch(r){if(pe(r))return{data:null,error:r};throw r}})}exists(t){return Ae(this,void 0,void 0,function*(){const s=this._getFinalPath(t);try{return yield zf(this.fetch,`${this.url}/object/${s}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(pe(r)&&r instanceof On){const n=r.originalError;if([400,404].includes(n?.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,s){const r=this._getFinalPath(t),n=[],i=s?.download?`download=${s.download===!0?"":s.download}`:"";i!==""&&n.push(i);const a=typeof s?.transform<"u"?"render/image":"object",l=this.transformOptsToQueryString(s?.transform||{});l!==""&&n.push(l);let u=n.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${r}${u}`)}}}remove(t){return Ae(this,void 0,void 0,function*(){try{return{data:yield dl(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(s){if(pe(s))return{data:null,error:s};throw s}})}list(t,s,r){return Ae(this,void 0,void 0,function*(){try{const n=Object.assign(Object.assign(Object.assign({},Gf),s),{prefix:t||""});return{data:yield St(this.fetch,`${this.url}/object/list/${this.bucketId}`,n,{headers:this.headers},r),error:null}}catch(n){if(pe(n))return{data:null,error:n};throw n}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const s=[];return t.width&&s.push(`width=${t.width}`),t.height&&s.push(`height=${t.height}`),t.resize&&s.push(`resize=${t.resize}`),t.format&&s.push(`format=${t.format}`),t.quality&&s.push(`quality=${t.quality}`),s.join("&")}}const Qf="2.7.1",Yf={"X-Client-Info":`storage-js/${Qf}`};var Yt=function(e,t,s,r){function n(i){return i instanceof s?i:new s(function(o){o(i)})}return new(s||(s=Promise))(function(i,o){function a(c){try{u(r.next(c))}catch(h){o(h)}}function l(c){try{u(r.throw(c))}catch(h){o(h)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class Xf{constructor(t,s={},r){this.url=t,this.headers=Object.assign(Object.assign({},Yf),s),this.fetch=fl(r)}listBuckets(){return Yt(this,void 0,void 0,function*(){try{return{data:yield Sr(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(pe(t))return{data:null,error:t};throw t}})}getBucket(t){return Yt(this,void 0,void 0,function*(){try{return{data:yield Sr(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(s){if(pe(s))return{data:null,error:s};throw s}})}createBucket(t,s={public:!1}){return Yt(this,void 0,void 0,function*(){try{return{data:yield St(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:s.public,file_size_limit:s.fileSizeLimit,allowed_mime_types:s.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(pe(r))return{data:null,error:r};throw r}})}updateBucket(t,s){return Yt(this,void 0,void 0,function*(){try{return{data:yield Kf(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:s.public,file_size_limit:s.fileSizeLimit,allowed_mime_types:s.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(pe(r))return{data:null,error:r};throw r}})}emptyBucket(t){return Yt(this,void 0,void 0,function*(){try{return{data:yield St(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(s){if(pe(s))return{data:null,error:s};throw s}})}deleteBucket(t){return Yt(this,void 0,void 0,function*(){try{return{data:yield dl(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(s){if(pe(s))return{data:null,error:s};throw s}})}}class Zf extends Xf{constructor(t,s={},r){super(t,s,r)}from(t){return new Jf(this.url,this.headers,t,this.fetch)}}const ed="2.50.5";let Ss="";typeof Deno<"u"?Ss="deno":typeof document<"u"?Ss="web":typeof navigator<"u"&&navigator.product==="ReactNative"?Ss="react-native":Ss="node";const td={"X-Client-Info":`supabase-js-${Ss}/${ed}`},sd={headers:td},rd={schema:"public"},nd={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},id={};var od=function(e,t,s,r){function n(i){return i instanceof s?i:new s(function(o){o(i)})}return new(s||(s=Promise))(function(i,o){function a(c){try{u(r.next(c))}catch(h){o(h)}}function l(c){try{u(r.throw(c))}catch(h){o(h)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const ad=e=>{let t;return e?t=e:typeof fetch>"u"?t=sl:t=fetch,(...s)=>t(...s)},ld=()=>typeof Headers>"u"?rl:Headers,cd=(e,t,s)=>{const r=ad(s),n=ld();return(i,o)=>od(void 0,void 0,void 0,function*(){var a;const l=(a=yield t())!==null&&a!==void 0?a:e;let u=new n(o?.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${l}`),r(i,Object.assign(Object.assign({},o),{headers:u}))})};var ud=function(e,t,s,r){function n(i){return i instanceof s?i:new s(function(o){o(i)})}return new(s||(s=Promise))(function(i,o){function a(c){try{u(r.next(c))}catch(h){o(h)}}function l(c){try{u(r.throw(c))}catch(h){o(h)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};function hd(e){return e.endsWith("/")?e:e+"/"}function fd(e,t){var s,r;const{db:n,auth:i,realtime:o,global:a}=e,{db:l,auth:u,realtime:c,global:h}=t,d={db:Object.assign(Object.assign({},l),n),auth:Object.assign(Object.assign({},u),i),realtime:Object.assign(Object.assign({},c),o),global:Object.assign(Object.assign(Object.assign({},h),a),{headers:Object.assign(Object.assign({},(s=h?.headers)!==null&&s!==void 0?s:{}),(r=a?.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>ud(this,void 0,void 0,function*(){return""})};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}const pl="2.70.0",ns=30*1e3,xn=3,tn=xn*ns,dd="http://localhost:9999",pd="supabase.auth.token",gd={"X-Client-Info":`gotrue-js/${pl}`},An="X-Supabase-Api-Version",gl={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},_d=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,md=6e5;class ni extends Error{constructor(t,s,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=s,this.code=r}}function H(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class vd extends ni{constructor(t,s,r){super(t,s,r),this.name="AuthApiError",this.status=s,this.code=r}}function yd(e){return H(e)&&e.name==="AuthApiError"}class _l extends ni{constructor(t,s){super(t),this.name="AuthUnknownError",this.originalError=s}}class At extends ni{constructor(t,s,r,n){super(t,r,n),this.name=s,this.status=r}}class vt extends At{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function wd(e){return H(e)&&e.name==="AuthSessionMissingError"}class rr extends At{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class nr extends At{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class ir extends At{constructor(t,s=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=s}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function bd(e){return H(e)&&e.name==="AuthImplicitGrantRedirectError"}class Eo extends At{constructor(t,s=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=s}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Rn extends At{constructor(t,s){super(t,"AuthRetryableFetchError",s,void 0)}}function sn(e){return H(e)&&e.name==="AuthRetryableFetchError"}class ko extends At{constructor(t,s,r){super(t,"AuthWeakPasswordError",s,"weak_password"),this.reasons=r}}class Ds extends At{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const Er="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),To=` 	
\r=`.split(""),Sd=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<To.length;t+=1)e[To[t].charCodeAt(0)]=-2;for(let t=0;t<Er.length;t+=1)e[Er[t].charCodeAt(0)]=t;return e})();function Oo(e,t,s){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;s(Er[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;s(Er[r]),t.queuedBits-=6}}function ml(e,t,s){const r=Sd[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)s(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function Po(e){const t=[],s=o=>{t.push(String.fromCodePoint(o))},r={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},i=o=>{Td(o,r,s)};for(let o=0;o<e.length;o+=1)ml(e.charCodeAt(o),n,i);return t.join("")}function Ed(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function kd(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){const n=(r-55296)*1024&65535;r=(e.charCodeAt(s+1)-56320&65535|n)+65536,s+=1}Ed(r,t)}}function Td(e,t,s){if(t.utf8seq===0){if(e<=127){s(e);return}for(let r=1;r<6;r+=1)if((e>>7-r&1)===0){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&s(t.codepoint)}}function Od(e){const t=[],s={queue:0,queuedBits:0},r=n=>{t.push(n)};for(let n=0;n<e.length;n+=1)ml(e.charCodeAt(n),s,r);return new Uint8Array(t)}function Pd(e){const t=[];return kd(e,s=>t.push(s)),new Uint8Array(t)}function xd(e){const t=[],s={queue:0,queuedBits:0},r=n=>{t.push(n)};return e.forEach(n=>Oo(n,s,r)),Oo(null,s,r),t.join("")}function Ad(e){return Math.round(Date.now()/1e3)+e}function Rd(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const Be=()=>typeof window<"u"&&typeof document<"u",jt={tested:!1,writable:!1},Ms=()=>{if(!Be())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(jt.tested)return jt.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),jt.tested=!0,jt.writable=!0}catch{jt.tested=!0,jt.writable=!1}return jt.writable};function Cd(e){const t={},s=new URL(e);if(s.hash&&s.hash[0]==="#")try{new URLSearchParams(s.hash.substring(1)).forEach((n,i)=>{t[i]=n})}catch{}return s.searchParams.forEach((r,n)=>{t[n]=r}),t}const vl=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...s)=>fe(async()=>{const{default:r}=await Promise.resolve().then(()=>ds);return{default:r}},void 0).then(({default:r})=>r(...s)):t=fetch,(...s)=>t(...s)},Id=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",yl=async(e,t,s)=>{await e.setItem(t,JSON.stringify(s))},or=async(e,t)=>{const s=await e.getItem(t);if(!s)return null;try{return JSON.parse(s)}catch{return s}},ar=async(e,t)=>{await e.removeItem(t)};class Mr{constructor(){this.promise=new Mr.promiseConstructor((t,s)=>{this.resolve=t,this.reject=s})}}Mr.promiseConstructor=Promise;function rn(e){const t=e.split(".");if(t.length!==3)throw new Ds("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!_d.test(t[r]))throw new Ds("JWT not in base64url format");return{header:JSON.parse(Po(t[0])),payload:JSON.parse(Po(t[1])),signature:Od(t[2]),raw:{header:t[0],payload:t[1]}}}async function jd(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function $d(e,t){return new Promise((r,n)=>{(async()=>{for(let i=0;i<1/0;i++)try{const o=await e(i);if(!t(i,null,o)){r(o);return}}catch(o){if(!t(i,o)){n(o);return}}})()})}function Ld(e){return("0"+e.toString(16)).substr(-2)}function Ud(){const t=new Uint32Array(56);if(typeof crypto>"u"){const s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=s.length;let n="";for(let i=0;i<56;i++)n+=s.charAt(Math.floor(Math.random()*r));return n}return crypto.getRandomValues(t),Array.from(t,Ld).join("")}async function Dd(e){const s=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",s),n=new Uint8Array(r);return Array.from(n).map(i=>String.fromCharCode(i)).join("")}async function Md(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const s=await Dd(e);return btoa(s).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Xt(e,t,s=!1){const r=Ud();let n=r;s&&(n+="/PASSWORD_RECOVERY"),await yl(e,`${t}-code-verifier`,n);const i=await Md(r);return[i,r===i?"plain":"s256"]}const Nd=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Bd(e){const t=e.headers.get(An);if(!t||!t.match(Nd))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function qd(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function Fd(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const Hd=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function Zt(e){if(!Hd.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var Wd=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]]);return s};const Ut=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Vd=[502,503,504];async function xo(e){var t;if(!Id(e))throw new Rn(Ut(e),0);if(Vd.includes(e.status))throw new Rn(Ut(e),e.status);let s;try{s=await e.json()}catch(i){throw new _l(Ut(i),i)}let r;const n=Bd(e);if(n&&n.getTime()>=gl["2024-01-01"].timestamp&&typeof s=="object"&&s&&typeof s.code=="string"?r=s.code:typeof s=="object"&&s&&typeof s.error_code=="string"&&(r=s.error_code),r){if(r==="weak_password")throw new ko(Ut(s),e.status,((t=s.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new vt}else if(typeof s=="object"&&s&&typeof s.weak_password=="object"&&s.weak_password&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.reasons.reduce((i,o)=>i&&typeof o=="string",!0))throw new ko(Ut(s),e.status,s.weak_password.reasons);throw new vd(Ut(s),e.status||500,r)}const Kd=(e,t,s,r)=>{const n={method:e,headers:t?.headers||{}};return e==="GET"?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t?.headers),n.body=JSON.stringify(r),Object.assign(Object.assign({},n),s))};async function K(e,t,s,r){var n;const i=Object.assign({},r?.headers);i[An]||(i[An]=gl["2024-01-01"].name),r?.jwt&&(i.Authorization=`Bearer ${r.jwt}`);const o=(n=r?.query)!==null&&n!==void 0?n:{};r?.redirectTo&&(o.redirect_to=r.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await zd(e,t,s+a,{headers:i,noResolveJson:r?.noResolveJson},{},r?.body);return r?.xform?r?.xform(l):{data:Object.assign({},l),error:null}}async function zd(e,t,s,r,n,i){const o=Kd(t,r,n,i);let a;try{a=await e(s,Object.assign({},o))}catch(l){throw console.error(l),new Rn(Ut(l),0)}if(a.ok||await xo(a),r?.noResolveJson)return a;try{return await a.json()}catch(l){await xo(l)}}function st(e){var t;let s=null;Yd(e)&&(s=Object.assign({},e),e.expires_at||(s.expires_at=Ad(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:s,user:r},error:null}}function Ao(e){const t=st(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((s,r)=>s&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function Et(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function Gd(e){return{data:e,error:null}}function Jd(e){const{action_link:t,email_otp:s,hashed_token:r,redirect_to:n,verification_type:i}=e,o=Wd(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:t,email_otp:s,hashed_token:r,redirect_to:n,verification_type:i},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function Qd(e){return e}function Yd(e){return e.access_token&&e.refresh_token&&e.expires_in}const nn=["global","local","others"];var Xd=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]]);return s};class Zd{constructor({url:t="",headers:s={},fetch:r}){this.url=t,this.headers=s,this.fetch=vl(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,s=nn[0]){if(nn.indexOf(s)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${nn.join(", ")}`);try{return await K(this.fetch,"POST",`${this.url}/logout?scope=${s}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(H(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,s={}){try{return await K(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:s.data},headers:this.headers,redirectTo:s.redirectTo,xform:Et})}catch(r){if(H(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:s}=t,r=Xd(t,["options"]),n=Object.assign(Object.assign({},r),s);return"newEmail"in r&&(n.new_email=r?.newEmail,delete n.newEmail),await K(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:n,headers:this.headers,xform:Jd,redirectTo:s?.redirectTo})}catch(s){if(H(s))return{data:{properties:null,user:null},error:s};throw s}}async createUser(t){try{return await K(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:Et})}catch(s){if(H(s))return{data:{user:null},error:s};throw s}}async listUsers(t){var s,r,n,i,o,a,l;try{const u={nextPage:null,lastPage:0,total:0},c=await K(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(s=t?.page)===null||s===void 0?void 0:s.toString())!==null&&r!==void 0?r:"",per_page:(i=(n=t?.perPage)===null||n===void 0?void 0:n.toString())!==null&&i!==void 0?i:""},xform:Qd});if(c.error)throw c.error;const h=await c.json(),d=(o=c.headers.get("x-total-count"))!==null&&o!==void 0?o:0,f=(l=(a=c.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return f.length>0&&(f.forEach(m=>{const v=parseInt(m.split(";")[0].split("=")[1].substring(0,1)),k=JSON.parse(m.split(";")[1].split("=")[1]);u[`${k}Page`]=v}),u.total=parseInt(d)),{data:Object.assign(Object.assign({},h),u),error:null}}catch(u){if(H(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){Zt(t);try{return await K(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:Et})}catch(s){if(H(s))return{data:{user:null},error:s};throw s}}async updateUserById(t,s){Zt(t);try{return await K(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:s,headers:this.headers,xform:Et})}catch(r){if(H(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,s=!1){Zt(t);try{return await K(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:s},xform:Et})}catch(r){if(H(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){Zt(t.userId);try{const{data:s,error:r}=await K(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:n=>({data:{factors:n},error:null})});return{data:s,error:r}}catch(s){if(H(s))return{data:null,error:s};throw s}}async _deleteFactor(t){Zt(t.userId),Zt(t.id);try{return{data:await K(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(s){if(H(s))return{data:null,error:s};throw s}}}const ep={getItem:e=>Ms()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Ms()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Ms()&&globalThis.localStorage.removeItem(e)}};function Ro(e={}){return{getItem:t=>e[t]||null,setItem:(t,s)=>{e[t]=s},removeItem:t=>{delete e[t]}}}function tp(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const es={debug:!!(globalThis&&Ms()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class wl extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class sp extends wl{}async function rp(e,t,s){es.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),es.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async n=>{if(n){es.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,n.name);try{return await s()}finally{es.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,n.name)}}else{if(t===0)throw es.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new sp(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(es.debug)try{const i=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(i,null,"  "))}catch(i){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",i)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await s()}}))}tp();const np={url:dd,storageKey:pd,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:gd,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Co(e,t,s){return await s()}class zs{constructor(t){var s,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=zs.nextInstanceID,zs.nextInstanceID+=1,this.instanceID>0&&Be()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const n=Object.assign(Object.assign({},np),t);if(this.logDebugMessages=!!n.debug,typeof n.debug=="function"&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new Zd({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=vl(n.fetch),this.lock=n.lock||Co,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.hasCustomAuthorizationHeader=n.hasCustomAuthorizationHeader,n.lock?this.lock=n.lock:Be()&&(!((s=globalThis?.navigator)===null||s===void 0)&&s.locks)?this.lock=rp:this.lock=Co,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?n.storage?this.storage=n.storage:Ms()?this.storage=ep:(this.memoryStorage={},this.storage=Ro(this.memoryStorage)):(this.memoryStorage={},this.storage=Ro(this.memoryStorage)),Be()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",i)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async i=>{this._debug("received broadcast notification from other tab or client",i),await this._notifyAllSubscribers(i.data.event,i.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${pl}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const s=Cd(window.location.href);let r="none";if(this._isImplicitGrantCallback(s)?r="implicit":await this._isPKCECallback(s)&&(r="pkce"),Be()&&this.detectSessionInUrl&&r!=="none"){const{data:n,error:i}=await this._getSessionFromURL(s,r);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),bd(i)){const l=(t=i.details)===null||t===void 0?void 0:t.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:i}}return await this._removeSession(),{error:i}}const{session:o,redirectType:a}=n;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),await this._saveSession(o),setTimeout(async()=>{a==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(s){return H(s)?{error:s}:{error:new _l("Unexpected error during initialization",s)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var s,r,n;try{const i=await K(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(s=t?.options)===null||s===void 0?void 0:s.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(n=t?.options)===null||n===void 0?void 0:n.captchaToken}},xform:st}),{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(H(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(t){var s,r,n;try{let i;if("email"in t){const{email:c,password:h,options:d}=t;let f=null,m=null;this.flowType==="pkce"&&([f,m]=await Xt(this.storage,this.storageKey)),i=await K(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:d?.emailRedirectTo,body:{email:c,password:h,data:(s=d?.data)!==null&&s!==void 0?s:{},gotrue_meta_security:{captcha_token:d?.captchaToken},code_challenge:f,code_challenge_method:m},xform:st})}else if("phone"in t){const{phone:c,password:h,options:d}=t;i=await K(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:h,data:(r=d?.data)!==null&&r!==void 0?r:{},channel:(n=d?.channel)!==null&&n!==void 0?n:"sms",gotrue_meta_security:{captcha_token:d?.captchaToken}},xform:st})}else throw new nr("You must provide either an email or phone number and a password");const{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(H(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(t){try{let s;if("email"in t){const{email:i,password:o,options:a}=t;s=await K(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:o,gotrue_meta_security:{captcha_token:a?.captchaToken}},xform:Ao})}else if("phone"in t){const{phone:i,password:o,options:a}=t;s=await K(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:o,gotrue_meta_security:{captcha_token:a?.captchaToken}},xform:Ao})}else throw new nr("You must provide either an email or phone number and a password");const{data:r,error:n}=s;return n?{data:{user:null,session:null},error:n}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new rr}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:n})}catch(s){if(H(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithOAuth(t){var s,r,n,i;return await this._handleProviderSignIn(t.provider,{redirectTo:(s=t.options)===null||s===void 0?void 0:s.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(n=t.options)===null||n===void 0?void 0:n.queryParams,skipBrowserRedirect:(i=t.options)===null||i===void 0?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:s}=t;if(s==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${s}"`)}async signInWithSolana(t){var s,r,n,i,o,a,l,u,c,h,d,f;let m,v;if("message"in t)m=t.message,v=t.signature;else{const{chain:k,wallet:A,statement:R,options:S}=t;let T;if(Be())if(typeof A=="object")T=A;else{const V=window;if("solana"in V&&typeof V.solana=="object"&&("signIn"in V.solana&&typeof V.solana.signIn=="function"||"signMessage"in V.solana&&typeof V.solana.signMessage=="function"))T=V.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof A!="object"||!S?.url)throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");T=A}const L=new URL((s=S?.url)!==null&&s!==void 0?s:window.location.href);if("signIn"in T&&T.signIn){const V=await T.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},S?.signInWithSolana),{version:"1",domain:L.host,uri:L.href}),R?{statement:R}:null));let q;if(Array.isArray(V)&&V[0]&&typeof V[0]=="object")q=V[0];else if(V&&typeof V=="object"&&"signedMessage"in V&&"signature"in V)q=V;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in q&&"signature"in q&&(typeof q.signedMessage=="string"||q.signedMessage instanceof Uint8Array)&&q.signature instanceof Uint8Array)m=typeof q.signedMessage=="string"?q.signedMessage:new TextDecoder().decode(q.signedMessage),v=q.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in T)||typeof T.signMessage!="function"||!("publicKey"in T)||typeof T!="object"||!T.publicKey||!("toBase58"in T.publicKey)||typeof T.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");m=[`${L.host} wants you to sign in with your Solana account:`,T.publicKey.toBase58(),...R?["",R,""]:[""],"Version: 1",`URI: ${L.href}`,`Issued At: ${(n=(r=S?.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&n!==void 0?n:new Date().toISOString()}`,...!((i=S?.signInWithSolana)===null||i===void 0)&&i.notBefore?[`Not Before: ${S.signInWithSolana.notBefore}`]:[],...!((o=S?.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${S.signInWithSolana.expirationTime}`]:[],...!((a=S?.signInWithSolana)===null||a===void 0)&&a.chainId?[`Chain ID: ${S.signInWithSolana.chainId}`]:[],...!((l=S?.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${S.signInWithSolana.nonce}`]:[],...!((u=S?.signInWithSolana)===null||u===void 0)&&u.requestId?[`Request ID: ${S.signInWithSolana.requestId}`]:[],...!((h=(c=S?.signInWithSolana)===null||c===void 0?void 0:c.resources)===null||h===void 0)&&h.length?["Resources",...S.signInWithSolana.resources.map(q=>`- ${q}`)]:[]].join(`
`);const V=await T.signMessage(new TextEncoder().encode(m),"utf8");if(!V||!(V instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");v=V}}try{const{data:k,error:A}=await K(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:m,signature:xd(v)},!((d=t.options)===null||d===void 0)&&d.captchaToken?{gotrue_meta_security:{captcha_token:(f=t.options)===null||f===void 0?void 0:f.captchaToken}}:null),xform:st});if(A)throw A;return!k||!k.session||!k.user?{data:{user:null,session:null},error:new rr}:(k.session&&(await this._saveSession(k.session),await this._notifyAllSubscribers("SIGNED_IN",k.session)),{data:Object.assign({},k),error:A})}catch(k){if(H(k))return{data:{user:null,session:null},error:k};throw k}}async _exchangeCodeForSession(t){const s=await or(this.storage,`${this.storageKey}-code-verifier`),[r,n]=(s??"").split("/");try{const{data:i,error:o}=await K(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:st});if(await ar(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new rr}:(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:n??null}),error:o})}catch(i){if(H(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(t){try{const{options:s,provider:r,token:n,access_token:i,nonce:o}=t,a=await K(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:n,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:s?.captchaToken}},xform:st}),{data:l,error:u}=a;return u?{data:{user:null,session:null},error:u}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new rr}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:u})}catch(s){if(H(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithOtp(t){var s,r,n,i,o;try{if("email"in t){const{email:a,options:l}=t;let u=null,c=null;this.flowType==="pkce"&&([u,c]=await Xt(this.storage,this.storageKey));const{error:h}=await K(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(s=l?.data)!==null&&s!==void 0?s:{},create_user:(r=l?.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:l?.captchaToken},code_challenge:u,code_challenge_method:c},redirectTo:l?.emailRedirectTo});return{data:{user:null,session:null},error:h}}if("phone"in t){const{phone:a,options:l}=t,{data:u,error:c}=await K(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(n=l?.data)!==null&&n!==void 0?n:{},create_user:(i=l?.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:l?.captchaToken},channel:(o=l?.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:u?.message_id},error:c}}throw new nr("You must provide either an email or phone number.")}catch(a){if(H(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(t){var s,r;try{let n,i;"options"in t&&(n=(s=t.options)===null||s===void 0?void 0:s.redirectTo,i=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:o,error:a}=await K(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:i}}),redirectTo:n,xform:st});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const l=o.session,u=o.user;return l?.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(n){if(H(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithSSO(t){var s,r,n;try{let i=null,o=null;return this.flowType==="pkce"&&([i,o]=await Xt(this.storage,this.storageKey)),await K(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(s=t.options)===null||s===void 0?void 0:s.redirectTo)!==null&&r!==void 0?r:void 0}),!((n=t?.options)===null||n===void 0)&&n.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:o}),headers:this.headers,xform:Gd})}catch(i){if(H(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:s},error:r}=t;if(r)throw r;if(!s)throw new vt;const{error:n}=await K(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:s.access_token});return{data:{user:null,session:null},error:n}})}catch(t){if(H(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const s=`${this.url}/resend`;if("email"in t){const{email:r,type:n,options:i}=t,{error:o}=await K(this.fetch,"POST",s,{headers:this.headers,body:{email:r,type:n,gotrue_meta_security:{captcha_token:i?.captchaToken}},redirectTo:i?.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in t){const{phone:r,type:n,options:i}=t,{data:o,error:a}=await K(this.fetch,"POST",s,{headers:this.headers,body:{phone:r,type:n,gotrue_meta_security:{captcha_token:i?.captchaToken}}});return{data:{user:null,session:null,messageId:o?.message_id},error:a}}throw new nr("You must provide either an email or phone number and a type")}catch(s){if(H(s))return{data:{user:null,session:null},error:s};throw s}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async s=>s))}async _acquireLock(t,s){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),n=(async()=>(await r,await s()))();return this.pendingInLock.push((async()=>{try{await n}catch{}})()),n}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=s();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const n=[...this.pendingInLock];await Promise.all(n),this.pendingInLock.splice(0,n.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const s=await this.__loadSession();return await t(s)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const s=await or(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",s),s!==null&&(this._isValidSession(s)?t=s:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<tn:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let o=this.suppressGetSessionWarning;t=new Proxy(t,{get:(l,u,c)=>(!o&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,u,c))})}return{data:{session:t},error:null}}const{session:n,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{session:null},error:i}:{data:{session:n},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await K(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:Et}):await this._useSession(async s=>{var r,n,i;const{data:o,error:a}=s;if(a)throw a;return!(!((r=o.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new vt}:await K(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(n=o.session)===null||n===void 0?void 0:n.access_token)!==null&&i!==void 0?i:void 0,xform:Et})})}catch(s){if(H(s))return wd(s)&&(await this._removeSession(),await ar(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:s};throw s}}async updateUser(t,s={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,s))}async _updateUser(t,s={}){try{return await this._useSession(async r=>{const{data:n,error:i}=r;if(i)throw i;if(!n.session)throw new vt;const o=n.session;let a=null,l=null;this.flowType==="pkce"&&t.email!=null&&([a,l]=await Xt(this.storage,this.storageKey));const{data:u,error:c}=await K(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:s?.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:a,code_challenge_method:l}),jwt:o.access_token,xform:Et});if(c)throw c;return o.user=u.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(H(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new vt;const s=Date.now()/1e3;let r=s,n=!0,i=null;const{payload:o}=rn(t.access_token);if(o.exp&&(r=o.exp,n=r<=s),n){const{session:a,error:l}=await this._callRefreshToken(t.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};i=a}else{const{data:a,error:l}=await this._getUser(t.access_token);if(l)throw l;i={access_token:t.access_token,refresh_token:t.refresh_token,user:a.user,token_type:"bearer",expires_in:r-s,expires_at:r},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(s){if(H(s))return{data:{session:null,user:null},error:s};throw s}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async s=>{var r;if(!t){const{data:o,error:a}=s;if(a)throw a;t=(r=o.session)!==null&&r!==void 0?r:void 0}if(!t?.refresh_token)throw new vt;const{session:n,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{user:null,session:null},error:i}:n?{data:{user:n.user,session:n},error:null}:{data:{user:null,session:null},error:null}})}catch(s){if(H(s))return{data:{user:null,session:null},error:s};throw s}}async _getSessionFromURL(t,s){try{if(!Be())throw new ir("No browser detected.");if(t.error||t.error_description||t.error_code)throw new ir(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(s){case"implicit":if(this.flowType==="pkce")throw new Eo("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new ir("Not a valid implicit grant flow url.");break;default:}if(s==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new Eo("No code detected.");const{data:R,error:S}=await this._exchangeCodeForSession(t.code);if(S)throw S;const T=new URL(window.location.href);return T.searchParams.delete("code"),window.history.replaceState(window.history.state,"",T.toString()),{data:{session:R.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:n,access_token:i,refresh_token:o,expires_in:a,expires_at:l,token_type:u}=t;if(!i||!a||!o||!u)throw new ir("No session defined in URL");const c=Math.round(Date.now()/1e3),h=parseInt(a);let d=c+h;l&&(d=parseInt(l));const f=d-c;f*1e3<=ns&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${f}s, should have been closer to ${h}s`);const m=d-h;c-m>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",m,d,c):c-m<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",m,d,c);const{data:v,error:k}=await this._getUser(i);if(k)throw k;const A={provider_token:r,provider_refresh_token:n,access_token:i,expires_in:h,expires_at:d,refresh_token:o,token_type:u,user:v.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:A,redirectType:t.type},error:null}}catch(r){if(H(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const s=await or(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&s)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async s=>{var r;const{data:n,error:i}=s;if(i)return{error:i};const o=(r=n.session)===null||r===void 0?void 0:r.access_token;if(o){const{error:a}=await this.admin.signOut(o,t);if(a&&!(yd(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return t!=="others"&&(await this._removeSession(),await ar(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const s=Rd(),r={id:s,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",s),this.stateChangeEmitters.delete(s)}};return this._debug("#onAuthStateChange()","registered callback with id",s),this.stateChangeEmitters.set(s,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(s)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async s=>{var r,n;try{const{data:{session:i},error:o}=s;if(o)throw o;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",t,"session",i)}catch(i){await((n=this.stateChangeEmitters.get(t))===null||n===void 0?void 0:n.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",i),console.error(i)}})}async resetPasswordForEmail(t,s={}){let r=null,n=null;this.flowType==="pkce"&&([r,n]=await Xt(this.storage,this.storageKey,!0));try{return await K(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:n,gotrue_meta_security:{captcha_token:s.captchaToken}},headers:this.headers,redirectTo:s.redirectTo})}catch(i){if(H(i))return{data:null,error:i};throw i}}async getUserIdentities(){var t;try{const{data:s,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=s.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(s){if(H(s))return{data:null,error:s};throw s}}async linkIdentity(t){var s;try{const{data:r,error:n}=await this._useSession(async i=>{var o,a,l,u,c;const{data:h,error:d}=i;if(d)throw d;const f=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(o=t.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=t.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=t.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await K(this.fetch,"GET",f,{headers:this.headers,jwt:(c=(u=h.session)===null||u===void 0?void 0:u.access_token)!==null&&c!==void 0?c:void 0})});if(n)throw n;return Be()&&!(!((s=t.options)===null||s===void 0)&&s.skipBrowserRedirect)&&window.location.assign(r?.url),{data:{provider:t.provider,url:r?.url},error:null}}catch(r){if(H(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async s=>{var r,n;const{data:i,error:o}=s;if(o)throw o;return await K(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(n=(r=i.session)===null||r===void 0?void 0:r.access_token)!==null&&n!==void 0?n:void 0})})}catch(s){if(H(s))return{data:null,error:s};throw s}}async _refreshAccessToken(t){const s=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(s,"begin");try{const r=Date.now();return await $d(async n=>(n>0&&await jd(200*Math.pow(2,n-1)),this._debug(s,"refreshing attempt",n),await K(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:st})),(n,i)=>{const o=200*Math.pow(2,n);return i&&sn(i)&&Date.now()+o-r<ns})}catch(r){if(this._debug(s,"error",r),H(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(s,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,s){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:s.redirectTo,scopes:s.scopes,queryParams:s.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",s,"url",r),Be()&&!s.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const s="#_recoverAndRefresh()";this._debug(s,"begin");try{const r=await or(this.storage,this.storageKey);if(this._debug(s,"session from storage",r),!this._isValidSession(r)){this._debug(s,"session is not valid"),r!==null&&await this._removeSession();return}const n=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<tn;if(this._debug(s,`session has${n?"":" not"} expired with margin of ${tn}s`),n){if(this.autoRefreshToken&&r.refresh_token){const{error:i}=await this._callRefreshToken(r.refresh_token);i&&(console.error(i),sn(i)||(this._debug(s,"refresh failed with a non-retryable error, removing the session",i),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(s,"error",r),console.error(r);return}finally{this._debug(s,"end")}}async _callRefreshToken(t){var s,r;if(!t)throw new vt;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const n=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{this.refreshingDeferred=new Mr;const{data:i,error:o}=await this._refreshAccessToken(t);if(o)throw o;if(!i.session)throw new vt;await this._saveSession(i.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const a={session:i.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(i){if(this._debug(n,"error",i),H(i)){const o={session:null,error:i};return sn(i)||await this._removeSession(),(s=this.refreshingDeferred)===null||s===void 0||s.resolve(o),o}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(i),i}finally{this.refreshingDeferred=null,this._debug(n,"end")}}async _notifyAllSubscribers(t,s,r=!0){const n=`#_notifyAllSubscribers(${t})`;this._debug(n,"begin",s,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:s});const i=[],o=Array.from(this.stateChangeEmitters.values()).map(async a=>{try{await a.callback(t,s)}catch(l){i.push(l)}});if(await Promise.all(o),i.length>0){for(let a=0;a<i.length;a+=1)console.error(i[a]);throw i[0]}}finally{this._debug(n,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await yl(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await ar(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&Be()&&window?.removeEventListener&&window.removeEventListener("visibilitychange",t)}catch(s){console.error("removing visibilitychange callback failed",s)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),ns);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async s=>{const{data:{session:r}}=s;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const n=Math.floor((r.expires_at*1e3-t)/ns);this._debug("#_autoRefreshTokenTick()",`access token expires in ${n} ticks, a tick lasts ${ns}ms, refresh threshold is ${xn} ticks`),n<=xn&&await this._callRefreshToken(r.refresh_token)})}catch(s){console.error("Auto refresh tick failed with error. This is likely a transient error.",s)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof wl)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Be()||!window?.addEventListener)return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window?.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const s=`#_onVisibilityChanged(${t})`;this._debug(s,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(s,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,s,r){const n=[`provider=${encodeURIComponent(s)}`];if(r?.redirectTo&&n.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r?.scopes&&n.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[i,o]=await Xt(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(o)}`});n.push(a.toString())}if(r?.queryParams){const i=new URLSearchParams(r.queryParams);n.push(i.toString())}return r?.skipBrowserRedirect&&n.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${n.join("&")}`}async _unenroll(t){try{return await this._useSession(async s=>{var r;const{data:n,error:i}=s;return i?{data:null,error:i}:await K(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=n?.session)===null||r===void 0?void 0:r.access_token})})}catch(s){if(H(s))return{data:null,error:s};throw s}}async _enroll(t){try{return await this._useSession(async s=>{var r,n;const{data:i,error:o}=s;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:l,error:u}=await K(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(r=i?.session)===null||r===void 0?void 0:r.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((n=l?.totp)===null||n===void 0)&&n.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(s){if(H(s))return{data:null,error:s};throw s}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async s=>{var r;const{data:n,error:i}=s;if(i)return{data:null,error:i};const{data:o,error:a}=await K(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=n?.session)===null||r===void 0?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})})}catch(s){if(H(s))return{data:null,error:s};throw s}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async s=>{var r;const{data:n,error:i}=s;return i?{data:null,error:i}:await K(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=n?.session)===null||r===void 0?void 0:r.access_token})})}catch(s){if(H(s))return{data:null,error:s};throw s}})}async _challengeAndVerify(t){const{data:s,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:s.id,code:t.code})}async _listFactors(){const{data:{user:t},error:s}=await this.getUser();if(s)return{data:null,error:s};const r=t?.factors||[],n=r.filter(o=>o.factor_type==="totp"&&o.status==="verified"),i=r.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:r,totp:n,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var s,r;const{data:{session:n},error:i}=t;if(i)return{data:null,error:i};if(!n)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=rn(n.access_token);let a=null;o.aal&&(a=o.aal);let l=a;((r=(s=n.user.factors)===null||s===void 0?void 0:s.filter(h=>h.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(l="aal2");const c=o.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:c},error:null}}))}async fetchJwk(t,s={keys:[]}){let r=s.keys.find(o=>o.kid===t);if(r||(r=this.jwks.keys.find(o=>o.kid===t),r&&this.jwks_cached_at+md>Date.now()))return r;const{data:n,error:i}=await K(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!n.keys||n.keys.length===0)throw new Ds("JWKS is empty");if(this.jwks=n,this.jwks_cached_at=Date.now(),r=n.keys.find(o=>o.kid===t),!r)throw new Ds("No matching signing key found in JWKS");return r}async getClaims(t,s={keys:[]}){try{let r=t;if(!r){const{data:f,error:m}=await this.getSession();if(m||!f.session)return{data:null,error:m};r=f.session.access_token}const{header:n,payload:i,signature:o,raw:{header:a,payload:l}}=rn(r);if(qd(i.exp),!n.kid||n.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:f}=await this.getUser(r);if(f)throw f;return{data:{claims:i,header:n,signature:o},error:null}}const u=Fd(n.alg),c=await this.fetchJwk(n.kid,s),h=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,h,o,Pd(`${a}.${l}`)))throw new Ds("Invalid JWT signature");return{data:{claims:i,header:n,signature:o},error:null}}catch(r){if(H(r))return{data:null,error:r};throw r}}}zs.nextInstanceID=0;const ip=zs;class op extends ip{constructor(t){super(t)}}var ap=function(e,t,s,r){function n(i){return i instanceof s?i:new s(function(o){o(i)})}return new(s||(s=Promise))(function(i,o){function a(c){try{u(r.next(c))}catch(h){o(h)}}function l(c){try{u(r.throw(c))}catch(h){o(h)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class lp{constructor(t,s,r){var n,i,o;if(this.supabaseUrl=t,this.supabaseKey=s,!t)throw new Error("supabaseUrl is required.");if(!s)throw new Error("supabaseKey is required.");const a=hd(t),l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const u=`sb-${l.hostname.split(".")[0]}-auth-token`,c={db:rd,realtime:id,auth:Object.assign(Object.assign({},nd),{storageKey:u}),global:sd},h=fd(r??{},c);this.storageKey=(n=h.auth.storageKey)!==null&&n!==void 0?n:"",this.headers=(i=h.global.headers)!==null&&i!==void 0?i:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(d,f)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(f)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=h.auth)!==null&&o!==void 0?o:{},this.headers,h.global.fetch),this.fetch=cd(s,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new Tf(new URL("rest/v1",l).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),h.accessToken||this._listenForAuthEvents()}get functions(){return new hf(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Zf(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,s={},r={}){return this.rest.rpc(t,s,r)}channel(t,s={config:{}}){return this.realtime.channel(t,s)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,s;return ap(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(s=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&s!==void 0?s:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:s,detectSessionInUrl:r,storage:n,storageKey:i,flowType:o,lock:a,debug:l},u,c){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new op({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),u),storageKey:i,autoRefreshToken:t,persistSession:s,detectSessionInUrl:r,storage:n,flowType:o,lock:a,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new Bf(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t?.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((s,r)=>{this._handleTokenChanged(s,"CLIENT",r?.access_token)})}_handleTokenChanged(t,s,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),s=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const cp=(e,t,s)=>new lp(e,t,s),up="https://your-project-ref.supabase.co",hp="your_supabase_anon_key_here",Q=cp(up,hp,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"}}),Ns={getCurrentUser:async()=>{const{data:{session:e}}=await Q.auth.getSession();return e?.user||null},getSession:async()=>await Q.auth.getSession(),signInWithLine:async()=>await Q.auth.signInWithOAuth({provider:"line",options:{redirectTo:`${window.location.origin}/auth/callback`,scopes:"profile openid email"}}),signInWithOAuth:async(e,t)=>await Q.auth.signInWithOAuth({provider:e,options:{redirectTo:t||`${window.location.origin}/auth/callback`}}),signOut:async()=>await Q.auth.signOut(),onAuthStateChange:e=>Q.auth.onAuthStateChange(e),refreshSession:async()=>await Q.auth.refreshSession()},ws={users:{getByLineUserId:async e=>await Q.from("users").select("*").eq("line_user_id",e).single(),getById:async e=>await Q.from("users").select("*").eq("id",e).single(),create:async e=>await Q.from("users").insert(e).select().single(),update:async(e,t)=>await Q.from("users").update(t).eq("id",e).select().single(),upsert:async e=>await Q.from("users").upsert(e,{onConflict:"line_user_id"}).select().single()},staff:{getActive:async()=>await Q.from("staff_members").select("*").eq("is_active",!0).order("name"),getById:async e=>await Q.from("staff_members").select("*").eq("id",e).single(),getAvailableTimeSlots:async(e,t,s=60)=>await Q.rpc("get_available_time_slots",{p_staff_id:e,p_date:t,p_service_duration:s})},services:{getActive:async()=>await Q.from("service_items").select("*").eq("is_active",!0).order("category, name"),getById:async e=>await Q.from("service_items").select("*").eq("id",e).single(),getByCategory:async e=>await Q.from("service_items").select("*").eq("category",e).eq("is_active",!0).order("name"),getCategories:async()=>await Q.from("service_items").select("category").eq("is_active",!0).not("category","is",null).order("category")},bookings:{create:async e=>await Q.from("bookings").insert(e).select(`
          *,
          staff:staff_members(*),
          service:service_items(*)
        `).single(),getByUserId:async e=>await Q.from("bookings").select(`
          *,
          staff:staff_members(*),
          service:service_items(*)
        `).eq("user_id",e).order("booking_date",{ascending:!1}),getById:async e=>await Q.from("bookings").select(`
          *,
          staff:staff_members(*),
          service:service_items(*),
          user:users(*)
        `).eq("id",e).single(),updateStatus:async(e,t)=>await Q.from("bookings").update({status:t}).eq("id",e).select().single(),cancel:async(e,t)=>await Q.from("bookings").update({status:"cancelled"}).eq("id",e).eq("user_id",t).select().single(),checkAvailability:async(e,t,s,r)=>{let n=Q.from("bookings").select("id").eq("staff_id",e).eq("booking_date",t).eq("booking_time",s).in("status",["pending","confirmed"]);return r&&(n=n.neq("id",r)),await n},getByStaffAndDate:async(e,t)=>await Q.from("bookings").select(`
          *,
          service:service_items(duration)
        `).eq("staff_id",e).eq("booking_date",t).in("status",["pending","confirmed"]).order("booking_time")},tickets:{getByUserId:async e=>await Q.from("tickets").select("*").eq("user_id",e).order("created_at",{ascending:!1}),getAvailableByUserId:async e=>{const t=new Date().toISOString().split("T")[0];return await Q.from("tickets").select("*").eq("user_id",e).eq("is_used",!1).or(`expiry_date.is.null,expiry_date.gte.${t}`).order("expiry_date")},getById:async e=>await Q.from("tickets").select("*").eq("id",e).single(),create:async e=>await Q.from("tickets").insert(e).select().single(),use:async(e,t)=>await Q.from("tickets").update({is_used:!0,used_at:new Date().toISOString(),booking_id:t}).eq("id",e).eq("is_used",!1).select().single(),useBatch:async(e,t)=>await Q.from("tickets").update({is_used:!0,used_at:new Date().toISOString(),booking_id:t}).in("id",e).eq("is_used",!1).select()},query:{rpc:async(e,t)=>await Q.rpc(e,t),sql:async e=>await Q.rpc("execute_sql",{query:e})}},gt={channelId:"your_line_channel_id_here",redirectUri:`${window.location.origin}/auth/callback`,scope:"profile openid email",state:"line_login_state"},lr={authorize:"https://access.line.me/oauth2/v2.1/authorize",token:"https://api.line.me/oauth2/v2.1/token",profile:"https://api.line.me/v2/profile",verify:"https://api.line.me/oauth2/v2.1/verify"};class $t{static isConfigured(){return!0}static generateAuthUrl(){if(!this.isConfigured())throw new Error("LINE Login 尚未配置，請檢查環境變數");const t=new URLSearchParams({response_type:"code",client_id:gt.channelId,redirect_uri:gt.redirectUri,state:gt.state,scope:gt.scope,nonce:this.generateNonce()});return`${lr.authorize}?${t.toString()}`}static redirectToLineLogin(){const t=this.generateAuthUrl();window.location.href=t}static async signInWithSupabase(){try{const{data:t,error:s}=await Ns.signInWithLine();if(s)throw console.error("Supabase LINE Login 錯誤:",s),new Error(s.message||"LINE 登入失敗");return t}catch(t){throw console.error("LINE Login 失敗:",t),t}}static async handleCallback(t,s){try{if(s!==gt.state)throw new Error("無效的 state 參數");const r=await this.exchangeCodeForToken(t),n=await this.getLineProfile(r.access_token);return await this.createOrUpdateUser(n,r)}catch(r){throw console.error("處理 LINE 回調失敗:",r),r}}static async exchangeCodeForToken(t){const s=await fetch(lr.token,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({grant_type:"authorization_code",code:t,redirect_uri:gt.redirectUri,client_id:gt.channelId,client_secret:"your_line_channel_secret_here"})});if(!s.ok){const r=await s.json();throw new Error(`獲取 token 失敗: ${r.error_description||r.error}`)}return await s.json()}static async getLineProfile(t){const s=await fetch(lr.profile,{headers:{Authorization:`Bearer ${t}`}});if(!s.ok)throw new Error("獲取 LINE 用戶資料失敗");return await s.json()}static async createOrUpdateUser(t,s){try{const{data:r}=await ws.users.getByLineUserId(t.userId),n={line_user_id:t.userId,display_name:t.displayName,picture_url:t.pictureUrl,email:null,phone:null};if(r){const{data:i,error:o}=await ws.users.update(r.id,n);if(o)throw o;return i}else{const{data:i,error:o}=await ws.users.create(n);if(o)throw o;return await this.createWelcomeTickets(i.id),i}}catch(r){throw console.error("創建或更新用戶失敗:",r),r}}static async createWelcomeTickets(t){try{const s=[{user_id:t,ticket_type:"discount",title:"新會員專享",description:"首次預約享 9 折優惠",discount_value:10,discount_type:"percentage",expiry_date:new Date(Date.now()+2592e6).toISOString().split("T")[0]},{user_id:t,ticket_type:"cashback",title:"回饋金",description:"註冊完成獲得 NT$100 回饋金",discount_value:100,discount_type:"fixed_amount",expiry_date:new Date(Date.now()+7776e6).toISOString().split("T")[0]}];for(const r of s)await ws.tickets.create(r)}catch(s){console.error("創建歡迎票券失敗:",s)}}static async signOut(){try{await Ns.signOut()}catch(t){throw console.error("登出失敗:",t),t}}static async getCurrentUser(){try{const{data:{session:t}}=await Ns.getSession();if(!t?.user)return null;const{data:s}=await ws.users.getById(t.user.id);return s||null}catch(t){return console.error("獲取當前用戶失敗:",t),null}}static async isAuthenticated(){return!!await this.getCurrentUser()}static generateNonce(){return Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15)}static async verifyToken(t){try{const s=await fetch(`${lr.verify}?access_token=${t}`),r=await s.json();return s.ok&&r.client_id===gt.channelId}catch(s){return console.error("驗證 token 失敗:",s),!1}}}const _t={isConfigured:()=>$t.isConfigured(),signIn:()=>$t.signInWithSupabase(),signInDirect:()=>$t.redirectToLineLogin(),signOut:()=>$t.signOut(),getCurrentUser:()=>$t.getCurrentUser(),isAuthenticated:()=>$t.isAuthenticated(),handleCallback:(e,t)=>$t.handleCallback(e,t)},bl=sh("auth",()=>{const e=as(null),t=$e(()=>!!e.value),s=as(!1),r=as(null);return{user:e,isAuthenticated:t,isLoading:s,error:r,initAuth:async()=>{s.value=!0,r.value=null;try{const{data:{session:f}}=await Ns.getSession();if(f?.user){const m=await _t.getCurrentUser();m&&(e.value=m)}Ns.onAuthStateChange(async(m,v)=>{if(console.log("認證狀態變化:",m,v),m==="SIGNED_IN"&&v?.user){const k=await _t.getCurrentUser();k&&(e.value=k)}else m==="SIGNED_OUT"&&(e.value=null)})}catch(f){console.error("初始化認證失敗:",f),r.value=f instanceof Error?f.message:"初始化失敗"}finally{s.value=!1}},signInWithLine:async()=>{s.value=!0,r.value=null;try{await _t.signIn()}catch(f){const m=f instanceof Error?f.message:"LINE 登入失敗";throw r.value=m,console.error("LINE 登入失敗:",f),new Error(m)}finally{s.value=!1}},signInWithLineDirect:async()=>{s.value=!0,r.value=null;try{_t.signInDirect()}catch(f){const m=f instanceof Error?f.message:"LINE 登入失敗";throw r.value=m,console.error("LINE 登入失敗:",f),new Error(m)}finally{s.value=!1}},handleLineCallback:async(f,m)=>{s.value=!0,r.value=null;try{const v=await _t.handleCallback(f,m);return e.value=v,v}catch(v){const k=v instanceof Error?v.message:"登入回調處理失敗";throw r.value=k,console.error("處理 LINE 回調失敗:",v),new Error(k)}finally{s.value=!1}},signOut:async()=>{s.value=!0,r.value=null;try{await _t.signOut(),e.value=null}catch(f){const m=f instanceof Error?f.message:"登出失敗";throw r.value=m,console.error("登出失敗:",f),new Error(m)}finally{s.value=!1}},refreshUser:async()=>{try{const f=await _t.getCurrentUser();f&&(e.value=f)}catch(f){console.error("刷新用戶資料失敗:",f)}},setUser:f=>{e.value=f},clearError:()=>{r.value=null},checkAuth:async()=>{try{return await _t.isAuthenticated()}catch(f){return console.error("檢查認證狀態失敗:",f),!1}}}}),fp={id:"app"},dp={key:0,class:"fixed inset-0 bg-white z-50 flex items-center justify-center"},pp=Gn({__name:"App",setup(e){const t=bl();return pa(async()=>{try{await t.initAuth()}catch(s){console.error("初始化認證失敗:",s)}}),(s,r)=>(gn(),Si("div",fp,[lt(t).isLoading&&!lt(t).user?(gn(),Si("div",dp,r[0]||(r[0]=[Cs("div",{class:"text-center"},[Cs("div",{class:"w-12 h-12 border-4 border-line-green border-t-transparent rounded-full animate-spin mx-auto mb-4"}),Cs("p",{class:"text-gray-600"},"載入中...")],-1)]))):hu("",!0),ke(lt(tl))]))}}),gp=(e,t)=>{const s=e.__vccOpts||e;for(const[r,n]of t)s[r]=n;return s},_p=gp(pp,[["__scopeId","data-v-ac3e754a"]]),Sl=rf({history:jh("/"),routes:[{path:"/",name:"home",component:()=>fe(()=>import("./HomeView-BEN4q0em.js"),[])},{path:"/login",name:"login",component:()=>fe(()=>import("./LoginView-Di7ouMri.js"),[])},{path:"/auth/callback",name:"auth-callback",component:()=>fe(()=>import("./AuthCallbackView-CUmC4r2h.js"),[])},{path:"/booking",name:"booking",component:()=>fe(()=>import("./BookingLayout-D-sJMg24.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!0},children:[{path:"",redirect:"/booking/staff"},{path:"staff",name:"booking-staff",component:()=>fe(()=>import("./StaffSelectionView-BPEdZK_d.js"),__vite__mapDeps([2,1,3]))},{path:"service",name:"booking-service",component:()=>fe(()=>import("./ServiceSelectionView-BXCCXftL.js"),__vite__mapDeps([4,1,5]))},{path:"datetime",name:"booking-datetime",component:()=>fe(()=>import("./DateTimeSelectionView-Pa-e5TkS.js"),__vite__mapDeps([6,1]))},{path:"confirm",name:"booking-confirm",component:()=>fe(()=>import("./BookingConfirmView-BSc_oNxr.js"),__vite__mapDeps([7,1]))}]},{path:"/member",name:"member",component:()=>fe(()=>import("./MemberLayout-D3umQnas.js"),[]),meta:{requiresAuth:!0},children:[{path:"",name:"member-profile",component:()=>fe(()=>import("./ProfileView-DrySnFLU.js"),[])},{path:"bookings",name:"member-bookings",component:()=>fe(()=>import("./BookingsView-v11bV0Bf.js"),__vite__mapDeps([8,1]))},{path:"tickets",name:"member-tickets",component:()=>fe(()=>import("./TicketsView-BhN2-7Ve.js"),__vite__mapDeps([9,1]))}]},{path:"/success",name:"booking-success",component:()=>fe(()=>import("./BookingSuccessView-CTwiRqd0.js"),__vite__mapDeps([10,1])),meta:{requiresAuth:!0}},{path:"/:pathMatch(.*)*",name:"not-found",component:()=>fe(()=>import("./NotFoundView-CMnn5Opm.js"),[])}]});Sl.beforeEach(async(e,t,s)=>{const r=bl();if(e.meta.requiresAuth&&!r.isAuthenticated){s("/login");return}s()});const ii=zu(_p);ii.use(Qu());ii.use(Sl);ii.mount("#app");export{sh as A,ws as B,it as F,gp as _,Cs as a,lt as b,Si as c,Gn as d,Ep as e,wp as f,$e as g,pa as h,hu as i,kp as j,yp as k,_t as l,ke as m,vp as n,gn as o,Un as p,mp as q,as as r,pc as s,$l as t,bl as u,bp as v,As as w,uu as x,Gs as y,Sp as z};
