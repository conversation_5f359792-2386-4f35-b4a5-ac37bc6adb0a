{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/router/index.ts", "../../src/services/lineauth.ts", "../../src/services/supabase.ts", "../../src/stores/auth.ts", "../../src/stores/booking.ts", "../../src/types/database.ts", "../../src/types/index.ts", "../../src/views/authcallbackview.vue", "../../src/views/bookingsuccessview.vue", "../../src/views/homeview.vue", "../../src/views/loginview.vue", "../../src/views/notfoundview.vue", "../../src/views/booking/bookingconfirmview.vue", "../../src/views/booking/bookinglayout.vue", "../../src/views/booking/datetimeselectionview.vue", "../../src/views/booking/serviceselectionview.vue", "../../src/views/booking/staffselectionview.vue", "../../src/views/member/bookingsview.vue", "../../src/views/member/memberlayout.vue", "../../src/views/member/profileview.vue", "../../src/views/member/ticketsview.vue"], "version": "5.8.3"}