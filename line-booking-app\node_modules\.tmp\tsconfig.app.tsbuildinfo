{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/components/helloworld.vue", "../../src/components/thewelcome.vue", "../../src/components/welcomeitem.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/router/index.ts", "../../src/services/lineauth.ts", "../../src/services/supabase.ts", "../../src/stores/booking.ts", "../../src/stores/counter.ts", "../../src/types/database.ts", "../../src/types/index.ts", "../../src/views/aboutview.vue", "../../src/views/authcallbackview.vue", "../../src/views/bookingsuccessview.vue", "../../src/views/homeview.vue", "../../src/views/loginview.vue", "../../src/views/notfoundview.vue", "../../src/views/booking/bookingconfirmview.vue", "../../src/views/booking/bookinglayout.vue", "../../src/views/booking/datetimeselectionview.vue", "../../src/views/booking/serviceselectionview.vue", "../../src/views/booking/staffselectionview.vue", "../../src/views/member/bookingsview.vue", "../../src/views/member/memberlayout.vue", "../../src/views/member/profileview.vue", "../../src/views/member/ticketsview.vue"], "errors": true, "version": "5.8.3"}