#!/usr/bin/env node

/**
 * 配置檢查腳本
 * 檢查部署前的所有必要配置
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 顏色輸出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`\n${colors.bold}${colors.blue}🔍 ${msg}${colors.reset}\n`)
}

class ConfigChecker {
  constructor() {
    this.errors = []
    this.warnings = []
    this.projectRoot = path.resolve(__dirname, '..')
  }

  // 檢查文件是否存在
  checkFileExists(filePath, description) {
    const fullPath = path.join(this.projectRoot, filePath)
    if (fs.existsSync(fullPath)) {
      log.success(`${description} 存在`)
      return true
    } else {
      this.errors.push(`${description} 不存在: ${filePath}`)
      log.error(`${description} 不存在: ${filePath}`)
      return false
    }
  }

  // 檢查環境變數文件
  checkEnvFile() {
    log.title('檢查環境變數配置')
    
    const envPath = path.join(this.projectRoot, '.env')
    const envExamplePath = path.join(this.projectRoot, '.env.example')
    
    // 檢查 .env.example 是否存在
    if (!this.checkFileExists('.env.example', '.env.example 範例文件')) {
      return false
    }

    // 檢查 .env 是否存在
    if (!fs.existsSync(envPath)) {
      this.warnings.push('未找到 .env 文件，請複製 .env.example 並填入實際值')
      log.warning('未找到 .env 文件')
      log.info('請執行: cp .env.example .env')
      return false
    }

    // 讀取環境變數
    const envContent = fs.readFileSync(envPath, 'utf8')
    const envVars = this.parseEnvFile(envContent)

    // 必要的環境變數
    const requiredVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY',
      'VITE_LINE_CHANNEL_ID',
      'VITE_LINE_CHANNEL_SECRET'
    ]

    let allPresent = true
    requiredVars.forEach(varName => {
      if (envVars[varName] && envVars[varName] !== 'your_value_here') {
        log.success(`${varName} 已設置`)
      } else {
        this.errors.push(`環境變數 ${varName} 未設置或使用預設值`)
        log.error(`環境變數 ${varName} 未設置`)
        allPresent = false
      }
    })

    return allPresent
  }

  // 解析 .env 文件
  parseEnvFile(content) {
    const vars = {}
    content.split('\n').forEach(line => {
      const trimmed = line.trim()
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=')
        if (key && valueParts.length > 0) {
          vars[key.trim()] = valueParts.join('=').trim()
        }
      }
    })
    return vars
  }

  // 檢查必要文件
  checkRequiredFiles() {
    log.title('檢查必要文件')
    
    const requiredFiles = [
      { path: 'package.json', desc: 'Package.json' },
      { path: 'vite.config.ts', desc: 'Vite 配置文件' },
      { path: 'tailwind.config.js', desc: 'Tailwind 配置文件' },
      { path: 'src/main.ts', desc: '主應用文件' },
      { path: 'database/init.sql', desc: '資料庫初始化腳本' },
      { path: 'database/rls-policies.sql', desc: 'RLS 政策腳本' }
    ]

    let allPresent = true
    requiredFiles.forEach(file => {
      if (!this.checkFileExists(file.path, file.desc)) {
        allPresent = false
      }
    })

    return allPresent
  }

  // 檢查依賴套件
  checkDependencies() {
    log.title('檢查依賴套件')
    
    const packageJsonPath = path.join(this.projectRoot, 'package.json')
    if (!fs.existsSync(packageJsonPath)) {
      this.errors.push('package.json 不存在')
      return false
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }

    const requiredDeps = [
      '@supabase/supabase-js',
      'vue',
      'vue-router',
      'pinia',
      'tailwindcss'
    ]

    let allPresent = true
    requiredDeps.forEach(dep => {
      if (dependencies[dep]) {
        log.success(`${dep} 已安裝 (${dependencies[dep]})`)
      } else {
        this.errors.push(`缺少依賴: ${dep}`)
        log.error(`缺少依賴: ${dep}`)
        allPresent = false
      }
    })

    return allPresent
  }

  // 檢查建構配置
  checkBuildConfig() {
    log.title('檢查建構配置')
    
    // 檢查 TypeScript 配置
    if (this.checkFileExists('tsconfig.json', 'TypeScript 配置')) {
      // 可以進一步檢查 tsconfig 內容
    }

    // 檢查 Vite 配置
    if (this.checkFileExists('vite.config.ts', 'Vite 配置')) {
      // 可以進一步檢查 vite config 內容
    }

    return true
  }

  // 檢查資料庫腳本
  checkDatabaseScripts() {
    log.title('檢查資料庫腳本')
    
    const initSqlPath = path.join(this.projectRoot, 'database/init.sql')
    const rlsSqlPath = path.join(this.projectRoot, 'database/rls-policies.sql')

    let valid = true

    if (fs.existsSync(initSqlPath)) {
      const content = fs.readFileSync(initSqlPath, 'utf8')
      if (content.includes('CREATE TABLE') && content.includes('users')) {
        log.success('資料庫初始化腳本內容正確')
      } else {
        this.warnings.push('資料庫初始化腳本可能不完整')
        log.warning('資料庫初始化腳本可能不完整')
      }
    }

    if (fs.existsSync(rlsSqlPath)) {
      const content = fs.readFileSync(rlsSqlPath, 'utf8')
      if (content.includes('ROW LEVEL SECURITY') && content.includes('POLICY')) {
        log.success('RLS 政策腳本內容正確')
      } else {
        this.warnings.push('RLS 政策腳本可能不完整')
        log.warning('RLS 政策腳本可能不完整')
      }
    }

    return valid
  }

  // 執行所有檢查
  async runAllChecks() {
    console.log(`${colors.bold}${colors.blue}🚀 LINE 預約系統配置檢查${colors.reset}\n`)

    const checks = [
      () => this.checkRequiredFiles(),
      () => this.checkDependencies(),
      () => this.checkEnvFile(),
      () => this.checkBuildConfig(),
      () => this.checkDatabaseScripts()
    ]

    let allPassed = true
    for (const check of checks) {
      if (!check()) {
        allPassed = false
      }
    }

    // 顯示總結
    this.showSummary(allPassed)
    
    return allPassed
  }

  // 顯示檢查總結
  showSummary(allPassed) {
    console.log(`\n${colors.bold}📊 檢查總結${colors.reset}\n`)

    if (allPassed && this.errors.length === 0) {
      log.success('所有檢查通過！系統已準備好部署')
      console.log(`\n${colors.green}${colors.bold}🎉 下一步：執行部署命令${colors.reset}`)
      console.log(`${colors.blue}npm run deploy${colors.reset}`)
    } else {
      if (this.errors.length > 0) {
        console.log(`${colors.red}${colors.bold}❌ 發現 ${this.errors.length} 個錯誤：${colors.reset}`)
        this.errors.forEach((error, index) => {
          console.log(`${colors.red}${index + 1}. ${error}${colors.reset}`)
        })
      }

      if (this.warnings.length > 0) {
        console.log(`\n${colors.yellow}${colors.bold}⚠️  發現 ${this.warnings.length} 個警告：${colors.reset}`)
        this.warnings.forEach((warning, index) => {
          console.log(`${colors.yellow}${index + 1}. ${warning}${colors.reset}`)
        })
      }

      console.log(`\n${colors.blue}請修復上述問題後重新執行檢查${colors.reset}`)
    }
  }
}

// 執行檢查
const checker = new ConfigChecker()
checker.runAllChecks().then(success => {
  process.exit(success ? 0 : 1)
}).catch(error => {
  console.error('檢查過程中發生錯誤:', error)
  process.exit(1)
})
