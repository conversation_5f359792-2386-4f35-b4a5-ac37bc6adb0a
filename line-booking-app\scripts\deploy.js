#!/usr/bin/env node

/**
 * 自動化部署腳本
 * 協助用戶完成整個部署流程
 */

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'
import readline from 'readline'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 顏色輸出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  step: (msg) => console.log(`\n${colors.bold}${colors.cyan}🚀 ${msg}${colors.reset}\n`),
  title: (msg) => console.log(`\n${colors.bold}${colors.blue}${msg}${colors.reset}\n`)
}

class DeploymentManager {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..')
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
  }

  // 詢問用戶輸入
  async askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(`${colors.cyan}❓ ${question}${colors.reset} `, resolve)
    })
  }

  // 確認操作
  async confirmAction(message) {
    const answer = await this.askQuestion(`${message} (y/N): `)
    return answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes'
  }

  // 執行命令
  execCommand(command, description) {
    try {
      log.info(`執行: ${command}`)
      const output = execSync(command, { 
        cwd: this.projectRoot, 
        stdio: 'pipe',
        encoding: 'utf8'
      })
      log.success(description)
      return output
    } catch (error) {
      log.error(`${description} 失敗: ${error.message}`)
      throw error
    }
  }

  // 檢查工具是否安裝
  checkToolInstalled(command, toolName) {
    try {
      execSync(`${command} --version`, { stdio: 'pipe' })
      log.success(`${toolName} 已安裝`)
      return true
    } catch (error) {
      log.error(`${toolName} 未安裝`)
      return false
    }
  }

  // 步驟 1: 預檢查
  async preDeploymentCheck() {
    log.step('步驟 1: 預檢查')

    // 檢查配置
    log.info('執行配置檢查...')
    try {
      this.execCommand('node scripts/check-config.js', '配置檢查')
    } catch (error) {
      log.error('配置檢查失敗，請先修復配置問題')
      return false
    }

    // 檢查建構
    log.info('測試建構...')
    try {
      this.execCommand('npm run build', '建構測試')
      log.success('建構成功')
    } catch (error) {
      log.error('建構失敗，請檢查程式碼')
      return false
    }

    return true
  }

  // 步驟 2: 選擇部署平台
  async selectDeploymentPlatform() {
    log.step('步驟 2: 選擇部署平台')

    console.log('可用的部署平台:')
    console.log('1. Vercel (推薦)')
    console.log('2. Netlify')
    console.log('3. 手動部署')

    const choice = await this.askQuestion('請選擇部署平台 (1-3): ')

    switch (choice) {
      case '1':
        return await this.deployToVercel()
      case '2':
        return await this.deployToNetlify()
      case '3':
        return await this.manualDeployment()
      default:
        log.error('無效選擇')
        return false
    }
  }

  // Vercel 部署
  async deployToVercel() {
    log.title('Vercel 部署')

    // 檢查 Vercel CLI
    if (!this.checkToolInstalled('vercel', 'Vercel CLI')) {
      const install = await this.confirmAction('是否安裝 Vercel CLI?')
      if (install) {
        this.execCommand('npm install -g vercel', 'Vercel CLI 安裝')
      } else {
        return false
      }
    }

    // 登入 Vercel
    const needLogin = await this.confirmAction('需要登入 Vercel 嗎?')
    if (needLogin) {
      this.execCommand('vercel login', 'Vercel 登入')
    }

    // 部署
    log.info('開始部署到 Vercel...')
    try {
      const output = this.execCommand('vercel --prod', 'Vercel 部署')
      
      // 提取部署 URL
      const urlMatch = output.match(/https:\/\/[^\s]+/)
      if (urlMatch) {
        const deployUrl = urlMatch[0]
        log.success(`部署成功！URL: ${deployUrl}`)
        
        // 設置環境變數
        await this.setupVercelEnvVars()
        
        return deployUrl
      }
    } catch (error) {
      log.error('Vercel 部署失敗')
      return false
    }
  }

  // 設置 Vercel 環境變數
  async setupVercelEnvVars() {
    log.info('設置 Vercel 環境變數...')
    
    const envVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY',
      'VITE_LINE_CHANNEL_ID',
      'VITE_LINE_CHANNEL_SECRET'
    ]

    for (const varName of envVars) {
      const value = await this.askQuestion(`請輸入 ${varName}: `)
      if (value) {
        try {
          this.execCommand(`vercel env add ${varName} production`, `設置 ${varName}`)
          // 這裡需要手動輸入值，實際使用時可能需要調整
        } catch (error) {
          log.warning(`設置 ${varName} 失敗，請手動在 Vercel Dashboard 中設置`)
        }
      }
    }
  }

  // Netlify 部署
  async deployToNetlify() {
    log.title('Netlify 部署')

    // 檢查 Netlify CLI
    if (!this.checkToolInstalled('netlify', 'Netlify CLI')) {
      const install = await this.confirmAction('是否安裝 Netlify CLI?')
      if (install) {
        this.execCommand('npm install -g netlify-cli', 'Netlify CLI 安裝')
      } else {
        return false
      }
    }

    // 登入 Netlify
    const needLogin = await this.confirmAction('需要登入 Netlify 嗎?')
    if (needLogin) {
      this.execCommand('netlify login', 'Netlify 登入')
    }

    // 初始化和部署
    try {
      this.execCommand('netlify init', 'Netlify 初始化')
      this.execCommand('netlify deploy --prod', 'Netlify 部署')
      log.success('Netlify 部署成功')
      return true
    } catch (error) {
      log.error('Netlify 部署失敗')
      return false
    }
  }

  // 手動部署指引
  async manualDeployment() {
    log.title('手動部署指引')

    console.log('手動部署步驟:')
    console.log('1. 執行建構: npm run build')
    console.log('2. 將 dist/ 目錄上傳到您的伺服器')
    console.log('3. 設置環境變數')
    console.log('4. 配置 HTTPS 和域名')

    // 執行建構
    const build = await this.confirmAction('是否現在執行建構?')
    if (build) {
      this.execCommand('npm run build', '建構應用')
      log.info('建構完成，dist/ 目錄已準備好上傳')
    }

    return true
  }

  // 步驟 3: 後部署配置
  async postDeploymentSetup(deployUrl) {
    log.step('步驟 3: 後部署配置')

    if (deployUrl) {
      log.info(`部署 URL: ${deployUrl}`)
      
      console.log('\n需要更新的配置:')
      console.log(`1. LINE Channel Callback URL: ${deployUrl}/auth/callback`)
      console.log(`2. Supabase Auth Redirect URL: ${deployUrl}/auth/callback`)
      console.log(`3. Supabase Site URL: ${deployUrl}`)

      const updateConfigs = await this.confirmAction('是否已更新上述配置?')
      if (updateConfigs) {
        log.success('配置更新確認')
      } else {
        log.warning('請記得更新配置，否則功能可能無法正常運作')
      }
    }
  }

  // 步驟 4: 部署測試
  async deploymentTest(deployUrl) {
    log.step('步驟 4: 部署測試')

    if (deployUrl) {
      console.log('請測試以下功能:')
      console.log('1. 網站載入')
      console.log('2. LINE 登入')
      console.log('3. 預約流程')
      console.log('4. 會員中心')

      const testPassed = await this.confirmAction('所有測試是否通過?')
      if (testPassed) {
        log.success('部署測試通過！')
        return true
      } else {
        log.warning('請檢查失敗的功能並修復問題')
        return false
      }
    }

    return true
  }

  // 主要部署流程
  async deploy() {
    console.log(`${colors.bold}${colors.blue}🚀 LINE 預約系統自動部署${colors.reset}\n`)

    try {
      // 步驟 1: 預檢查
      if (!await this.preDeploymentCheck()) {
        log.error('預檢查失敗，部署中止')
        return false
      }

      // 步驟 2: 部署
      const deployUrl = await this.selectDeploymentPlatform()
      if (!deployUrl) {
        log.error('部署失敗')
        return false
      }

      // 步驟 3: 後部署配置
      await this.postDeploymentSetup(deployUrl)

      // 步驟 4: 測試
      const testSuccess = await this.deploymentTest(deployUrl)

      if (testSuccess) {
        console.log(`\n${colors.green}${colors.bold}🎉 部署成功完成！${colors.reset}`)
        if (deployUrl) {
          console.log(`${colors.blue}網站 URL: ${deployUrl}${colors.reset}`)
        }
      }

      return testSuccess

    } catch (error) {
      log.error(`部署過程中發生錯誤: ${error.message}`)
      return false
    } finally {
      this.rl.close()
    }
  }
}

// 執行部署
const deployer = new DeploymentManager()
deployer.deploy().then(success => {
  process.exit(success ? 0 : 1)
}).catch(error => {
  console.error('部署腳本執行錯誤:', error)
  process.exit(1)
})
