@import './base.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定義樣式 */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    background-color: #f9fafb;
    color: #111827;
  }
}

@layer components {
  .btn-primary {
    background-color: #00B900;
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.2s;
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .btn-primary:hover {
    background-color: #16a34a;
  }

  .btn-secondary {
    background-color: white;
    color: #374151;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    transition: all 0.2s;
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .btn-secondary:hover {
    background-color: #f9fafb;
  }

  .card {
    background-color: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
  }

  .input-field {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    transition: all 0.2s;
  }

  .input-field:focus {
    outline: none;
    ring: 2px;
    ring-color: #00B900;
    border-color: transparent;
  }
}

/* LINE 內置瀏覽器優化 */
@media screen and (max-width: 768px) {
  body {
    @apply text-base;
    -webkit-text-size-adjust: 100%;
  }

  .touch-action-manipulation {
    touch-action: manipulation;
  }
}

/* 防止縮放 */
@viewport {
  width: device-width;
  zoom: 1.0;
}
