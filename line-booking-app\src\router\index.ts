import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/auth/callback',
      name: 'auth-callback',
      component: () => import('../views/AuthCallbackView.vue'),
    },
    {
      path: '/booking',
      name: 'booking',
      component: () => import('../views/booking/BookingLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          redirect: '/booking/staff'
        },
        {
          path: 'staff',
          name: 'booking-staff',
          component: () => import('../views/booking/StaffSelectionView.vue'),
        },
        {
          path: 'service',
          name: 'booking-service',
          component: () => import('../views/booking/ServiceSelectionView.vue'),
        },
        {
          path: 'datetime',
          name: 'booking-datetime',
          component: () => import('../views/booking/DateTimeSelectionView.vue'),
        },
        {
          path: 'confirm',
          name: 'booking-confirm',
          component: () => import('../views/booking/BookingConfirmView.vue'),
        },
      ]
    },
    {
      path: '/member',
      name: 'member',
      component: () => import('../views/member/MemberLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'member-profile',
          component: () => import('../views/member/ProfileView.vue'),
        },
        {
          path: 'bookings',
          name: 'member-bookings',
          component: () => import('../views/member/BookingsView.vue'),
        },
        {
          path: 'tickets',
          name: 'member-tickets',
          component: () => import('../views/member/TicketsView.vue'),
        },
      ]
    },
    {
      path: '/success',
      name: 'booking-success',
      component: () => import('../views/BookingSuccessView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFoundView.vue'),
    },
  ],
})

// 路由守衛
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 檢查是否需要認證
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }

  next()
})

export default router
