import { supabase, auth, db } from './supabase'
import type { LineUserProfile, User } from '@/types'

// LINE Login 配置
const LINE_CONFIG = {
  channelId: import.meta.env.VITE_LINE_CHANNEL_ID || '',
  redirectUri: `${window.location.origin}/auth/callback`,
  scope: 'profile openid email',
  state: 'line_login_state' // 可以使用隨機字符串增加安全性
}

// LINE API 端點
const LINE_API = {
  authorize: 'https://access.line.me/oauth2/v2.1/authorize',
  token: 'https://api.line.me/oauth2/v2.1/token',
  profile: 'https://api.line.me/v2/profile',
  verify: 'https://api.line.me/oauth2/v2.1/verify'
}

/**
 * LINE Login 服務類
 */
export class LineAuthService {
  /**
   * 檢查 LINE Login 配置是否完整
   */
  static isConfigured(): boolean {
    return !!LINE_CONFIG.channelId
  }

  /**
   * 生成 LINE Login 授權 URL
   */
  static generateAuthUrl(): string {
    if (!this.isConfigured()) {
      throw new Error('LINE Login 尚未配置，請檢查環境變數')
    }

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: LINE_CONFIG.channelId,
      redirect_uri: LINE_CONFIG.redirectUri,
      state: LINE_CONFIG.state,
      scope: LINE_CONFIG.scope,
      nonce: this.generateNonce()
    })

    return `${LINE_API.authorize}?${params.toString()}`
  }

  /**
   * 直接跳轉到 LINE Login
   */
  static redirectToLineLogin(): void {
    const authUrl = this.generateAuthUrl()
    window.location.href = authUrl
  }

  /**
   * 使用 Supabase Auth 進行 LINE Login
   */
  static async signInWithSupabase() {
    try {
      const { data, error } = await auth.signInWithOAuth('line')
      
      if (error) {
        console.error('Supabase LINE Login 錯誤:', error)
        throw new Error(error.message || 'LINE 登入失敗')
      }

      return data
    } catch (error) {
      console.error('LINE Login 失敗:', error)
      throw error
    }
  }

  /**
   * 處理 LINE Login 回調
   */
  static async handleCallback(code: string, state: string): Promise<User> {
    try {
      // 驗證 state 參數
      if (state !== LINE_CONFIG.state) {
        throw new Error('無效的 state 參數')
      }

      // 獲取 access token
      const tokenData = await this.exchangeCodeForToken(code)
      
      // 獲取用戶資料
      const lineProfile = await this.getLineProfile(tokenData.access_token)
      
      // 創建或更新用戶
      const user = await this.createOrUpdateUser(lineProfile, tokenData)
      
      return user
    } catch (error) {
      console.error('處理 LINE 回調失敗:', error)
      throw error
    }
  }

  /**
   * 交換授權碼獲取 access token
   */
  private static async exchangeCodeForToken(code: string) {
    const response = await fetch(LINE_API.token, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: LINE_CONFIG.redirectUri,
        client_id: LINE_CONFIG.channelId,
        client_secret: import.meta.env.VITE_LINE_CHANNEL_SECRET || ''
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`獲取 token 失敗: ${errorData.error_description || errorData.error}`)
    }

    return await response.json()
  }

  /**
   * 獲取 LINE 用戶資料
   */
  private static async getLineProfile(accessToken: string): Promise<LineUserProfile> {
    const response = await fetch(LINE_API.profile, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    })

    if (!response.ok) {
      throw new Error('獲取 LINE 用戶資料失敗')
    }

    return await response.json()
  }

  /**
   * 創建或更新用戶資料
   */
  private static async createOrUpdateUser(lineProfile: LineUserProfile, tokenData: any): Promise<User> {
    try {
      // 檢查用戶是否已存在
      const { data: existingUser } = await db.users.getByLineUserId(lineProfile.userId)

      const userData = {
        line_user_id: lineProfile.userId,
        display_name: lineProfile.displayName,
        picture_url: lineProfile.pictureUrl,
        email: null, // LINE Login 可能不提供 email
        phone: null
      }

      if (existingUser) {
        // 更新現有用戶
        const { data: updatedUser, error } = await db.users.update(existingUser.id, userData)
        if (error) throw error
        return updatedUser
      } else {
        // 創建新用戶
        const { data: newUser, error } = await db.users.create(userData)
        if (error) throw error
        
        // 為新用戶創建歡迎票券
        await this.createWelcomeTickets(newUser.id)
        
        return newUser
      }
    } catch (error) {
      console.error('創建或更新用戶失敗:', error)
      throw error
    }
  }

  /**
   * 為新用戶創建歡迎票券
   */
  private static async createWelcomeTickets(userId: string) {
    try {
      const welcomeTickets = [
        {
          user_id: userId,
          ticket_type: 'discount' as const,
          title: '新會員專享',
          description: '首次預約享 9 折優惠',
          discount_value: 10,
          discount_type: 'percentage' as const,
          expiry_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30天後到期
        },
        {
          user_id: userId,
          ticket_type: 'cashback' as const,
          title: '回饋金',
          description: '註冊完成獲得 NT$100 回饋金',
          discount_value: 100,
          discount_type: 'fixed_amount' as const,
          expiry_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 90天後到期
        }
      ]

      for (const ticket of welcomeTickets) {
        await db.tickets.create(ticket)
      }
    } catch (error) {
      console.error('創建歡迎票券失敗:', error)
      // 不拋出錯誤，因為這不應該影響用戶註冊流程
    }
  }

  /**
   * 登出
   */
  static async signOut(): Promise<void> {
    try {
      await auth.signOut()
    } catch (error) {
      console.error('登出失敗:', error)
      throw error
    }
  }

  /**
   * 獲取當前用戶
   */
  static async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { session } } = await auth.getSession()
      
      if (!session?.user) {
        return null
      }

      // 從資料庫獲取完整的用戶資料
      const { data: user } = await db.users.getById(session.user.id)
      return user || null
    } catch (error) {
      console.error('獲取當前用戶失敗:', error)
      return null
    }
  }

  /**
   * 檢查用戶是否已登入
   */
  static async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser()
    return !!user
  }

  /**
   * 生成隨機 nonce
   */
  private static generateNonce(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15)
  }

  /**
   * 驗證 access token
   */
  static async verifyToken(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch(`${LINE_API.verify}?access_token=${accessToken}`)
      const data = await response.json()
      
      return response.ok && data.client_id === LINE_CONFIG.channelId
    } catch (error) {
      console.error('驗證 token 失敗:', error)
      return false
    }
  }
}

// 導出便利函數
export const lineAuth = {
  isConfigured: () => LineAuthService.isConfigured(),
  signIn: () => LineAuthService.signInWithSupabase(),
  signInDirect: () => LineAuthService.redirectToLineLogin(),
  signOut: () => LineAuthService.signOut(),
  getCurrentUser: () => LineAuthService.getCurrentUser(),
  isAuthenticated: () => LineAuthService.isAuthenticated(),
  handleCallback: (code: string, state: string) => LineAuthService.handleCallback(code, state)
}

export default lineAuth
