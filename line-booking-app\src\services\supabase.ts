import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

// Supabase 配置
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || ''
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || ''

// 檢查環境變數
if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase 環境變數未設置，請檢查 .env 文件')
}

// 創建 Supabase 客戶端
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
})

// 認證相關工具函數
export const auth = {
  // 獲取當前用戶
  getCurrentUser: async () => {
    const { data: { session } } = await supabase.auth.getSession()
    return session?.user || null
  },

  // 獲取當前會話
  getSession: async () => {
    return await supabase.auth.getSession()
  },

  // LINE Login (注意：Supabase 可能不直接支援 LINE provider)
  signInWithLine: async () => {
    return await supabase.auth.signInWithOAuth({
      provider: 'line' as any, // 使用 any 類型避免 TypeScript 錯誤
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        scopes: 'profile openid email'
      }
    })
  },

  // 使用 OAuth 登入（通用）
  signInWithOAuth: async (provider: 'google' | 'github', redirectTo?: string) => {
    return await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: redirectTo || `${window.location.origin}/auth/callback`
      }
    })
  },

  // 登出
  signOut: async () => {
    return await supabase.auth.signOut()
  },

  // 監聽認證狀態變化
  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback)
  },

  // 刷新會話
  refreshSession: async () => {
    return await supabase.auth.refreshSession()
  }
}

// 資料庫操作工具函數
export const db = {
  // 用戶相關操作
  users: {
    // 根據 LINE User ID 獲取用戶
    getByLineUserId: async (lineUserId: string) => {
      return await supabase
        .from('users')
        .select('*')
        .eq('line_user_id', lineUserId)
        .single()
    },

    // 根據 ID 獲取用戶
    getById: async (userId: string) => {
      return await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()
    },

    // 創建新用戶
    create: async (userData: any) => {
      return await supabase
        .from('users')
        .insert(userData)
        .select()
        .single()
    },

    // 更新用戶資料
    update: async (userId: string, userData: any) => {
      return await supabase
        .from('users')
        .update(userData)
        .eq('id', userId)
        .select()
        .single()
    },

    // 創建或更新用戶（upsert）
    upsert: async (userData: any) => {
      return await supabase
        .from('users')
        .upsert(userData, { onConflict: 'line_user_id' })
        .select()
        .single()
    }
  },

  // 服務人員相關操作
  staff: {
    // 獲取所有活躍的服務人員
    getActive: async () => {
      return await supabase
        .from('staff_members')
        .select('*')
        .eq('is_active', true)
        .order('name')
    },

    // 根據 ID 獲取服務人員
    getById: async (staffId: string) => {
      return await supabase
        .from('staff_members')
        .select('*')
        .eq('id', staffId)
        .single()
    },

    // 獲取服務人員的可用時間段
    getAvailableTimeSlots: async (staffId: string, date: string, serviceDuration: number = 60) => {
      return await supabase.rpc('get_available_time_slots', {
        p_staff_id: staffId,
        p_date: date,
        p_service_duration: serviceDuration
      })
    }
  },

  // 服務項目相關操作
  services: {
    // 獲取所有活躍的服務項目
    getActive: async () => {
      return await supabase
        .from('service_items')
        .select('*')
        .eq('is_active', true)
        .order('category, name')
    },

    // 根據 ID 獲取服務項目
    getById: async (serviceId: string) => {
      return await supabase
        .from('service_items')
        .select('*')
        .eq('id', serviceId)
        .single()
    },

    // 根據分類獲取服務項目
    getByCategory: async (category: string) => {
      return await supabase
        .from('service_items')
        .select('*')
        .eq('category', category)
        .eq('is_active', true)
        .order('name')
    },

    // 獲取所有分類
    getCategories: async () => {
      return await supabase
        .from('service_items')
        .select('category')
        .eq('is_active', true)
        .not('category', 'is', null)
        .order('category')
    }
  },

  // 預約相關操作
  bookings: {
    // 創建預約
    create: async (bookingData: any) => {
      return await supabase
        .from('bookings')
        .insert(bookingData)
        .select(`
          *,
          staff:staff_members(*),
          service:service_items(*)
        `)
        .single()
    },

    // 獲取用戶的預約記錄
    getByUserId: async (userId: string) => {
      return await supabase
        .from('bookings')
        .select(`
          *,
          staff:staff_members(*),
          service:service_items(*)
        `)
        .eq('user_id', userId)
        .order('booking_date', { ascending: false })
    },

    // 根據 ID 獲取預約
    getById: async (bookingId: string) => {
      return await supabase
        .from('bookings')
        .select(`
          *,
          staff:staff_members(*),
          service:service_items(*),
          user:users(*)
        `)
        .eq('id', bookingId)
        .single()
    },

    // 更新預約狀態
    updateStatus: async (bookingId: string, status: string) => {
      return await supabase
        .from('bookings')
        .update({ status })
        .eq('id', bookingId)
        .select()
        .single()
    },

    // 取消預約
    cancel: async (bookingId: string, userId: string) => {
      return await supabase
        .from('bookings')
        .update({ status: 'cancelled' })
        .eq('id', bookingId)
        .eq('user_id', userId)
        .select()
        .single()
    },

    // 檢查時間段可用性
    checkAvailability: async (staffId: string, date: string, time: string, excludeBookingId?: string) => {
      let query = supabase
        .from('bookings')
        .select('id')
        .eq('staff_id', staffId)
        .eq('booking_date', date)
        .eq('booking_time', time)
        .in('status', ['pending', 'confirmed'])

      if (excludeBookingId) {
        query = query.neq('id', excludeBookingId)
      }

      return await query
    },

    // 獲取服務人員在特定日期的預約
    getByStaffAndDate: async (staffId: string, date: string) => {
      return await supabase
        .from('bookings')
        .select(`
          *,
          service:service_items(duration)
        `)
        .eq('staff_id', staffId)
        .eq('booking_date', date)
        .in('status', ['pending', 'confirmed'])
        .order('booking_time')
    }
  },

  // 票券相關操作
  tickets: {
    // 獲取用戶的所有票券
    getByUserId: async (userId: string) => {
      return await supabase
        .from('tickets')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
    },

    // 獲取用戶的可用票券
    getAvailableByUserId: async (userId: string) => {
      const today = new Date().toISOString().split('T')[0]
      return await supabase
        .from('tickets')
        .select('*')
        .eq('user_id', userId)
        .eq('is_used', false)
        .or(`expiry_date.is.null,expiry_date.gte.${today}`)
        .order('expiry_date')
    },

    // 根據 ID 獲取票券
    getById: async (ticketId: string) => {
      return await supabase
        .from('tickets')
        .select('*')
        .eq('id', ticketId)
        .single()
    },

    // 創建票券
    create: async (ticketData: any) => {
      return await supabase
        .from('tickets')
        .insert(ticketData)
        .select()
        .single()
    },

    // 使用票券
    use: async (ticketId: string, bookingId: string) => {
      return await supabase
        .from('tickets')
        .update({
          is_used: true,
          used_at: new Date().toISOString(),
          booking_id: bookingId
        })
        .eq('id', ticketId)
        .eq('is_used', false) // 確保票券未被使用
        .select()
        .single()
    },

    // 批量使用票券
    useBatch: async (ticketIds: string[], bookingId: string) => {
      return await supabase
        .from('tickets')
        .update({
          is_used: true,
          used_at: new Date().toISOString(),
          booking_id: bookingId
        })
        .in('id', ticketIds)
        .eq('is_used', false)
        .select()
    }
  },

  // 通用查詢函數
  query: {
    // 執行自定義 RPC 函數
    rpc: async (functionName: string, params?: any) => {
      return await supabase.rpc(functionName, params)
    },

    // 執行原始 SQL 查詢（僅限開發環境）
    sql: async (query: string) => {
      if (import.meta.env.DEV) {
        console.warn('執行原始 SQL 查詢:', query)
      }
      return await supabase.rpc('execute_sql', { query })
    }
  }
}

export default supabase
