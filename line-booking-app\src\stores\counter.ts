import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { User } from '@/types'
import { lineAuth } from '@/services/lineAuth'
import { auth } from '@/services/supabase'

// 用戶認證 Store
export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => !!user.value)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 初始化認證狀態
  const initAuth = async () => {
    isLoading.value = true
    error.value = null
    try {
      // 檢查是否有現有的會話
      const { data: { session } } = await auth.getSession()

      if (session?.user) {
        // 從資料庫獲取完整的用戶資料
        const currentUser = await lineAuth.getCurrentUser()
        if (currentUser) {
          user.value = currentUser
        }
      }

      // 監聽認證狀態變化
      auth.onAuthStateChange(async (event, session) => {
        console.log('認證狀態變化:', event, session)

        if (event === 'SIGNED_IN' && session?.user) {
          const currentUser = await lineAuth.getCurrentUser()
          if (currentUser) {
            user.value = currentUser
          }
        } else if (event === 'SIGNED_OUT') {
          user.value = null
        }
      })
    } catch (error) {
      console.error('初始化認證失敗:', error)
      error.value = error instanceof Error ? error.message : '初始化失敗'
    } finally {
      isLoading.value = false
    }
  }

  // LINE 登入 (使用 Supabase Auth)
  const signInWithLine = async () => {
    isLoading.value = true
    error.value = null
    try {
      if (!lineAuth.isConfigured()) {
        throw new Error('LINE Login 尚未配置，請檢查環境變數')
      }

      await lineAuth.signIn()
      // 登入成功後，Supabase 會自動重定向到回調頁面
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'LINE 登入失敗'
      error.value = errorMessage
      console.error('LINE 登入失敗:', err)
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  // 直接 LINE 登入 (不使用 Supabase Auth)
  const signInWithLineDirect = async () => {
    isLoading.value = true
    error.value = null
    try {
      if (!lineAuth.isConfigured()) {
        throw new Error('LINE Login 尚未配置，請檢查環境變數')
      }

      lineAuth.signInDirect()
      // 這會直接重定向到 LINE，不需要等待回應
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'LINE 登入失敗'
      error.value = errorMessage
      console.error('LINE 登入失敗:', err)
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  // 處理 LINE 登入回調
  const handleLineCallback = async (code: string, state: string) => {
    isLoading.value = true
    error.value = null
    try {
      const userData = await lineAuth.handleCallback(code, state)
      user.value = userData
      return userData
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '登入回調處理失敗'
      error.value = errorMessage
      console.error('處理 LINE 回調失敗:', err)
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const signOut = async () => {
    isLoading.value = true
    error.value = null
    try {
      await lineAuth.signOut()
      user.value = null
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '登出失敗'
      error.value = errorMessage
      console.error('登出失敗:', err)
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  // 刷新用戶資料
  const refreshUser = async () => {
    try {
      const currentUser = await lineAuth.getCurrentUser()
      if (currentUser) {
        user.value = currentUser
      }
    } catch (err) {
      console.error('刷新用戶資料失敗:', err)
    }
  }

  // 設置用戶資料
  const setUser = (userData: User | null) => {
    user.value = userData
  }

  // 清除錯誤
  const clearError = () => {
    error.value = null
  }

  // 檢查是否已認證
  const checkAuth = async (): Promise<boolean> => {
    try {
      return await lineAuth.isAuthenticated()
    } catch (err) {
      console.error('檢查認證狀態失敗:', err)
      return false
    }
  }

  return {
    // 狀態
    user,
    isAuthenticated,
    isLoading,
    error,

    // 動作
    initAuth,
    signInWithLine,
    signInWithLineDirect,
    handleLineCallback,
    signOut,
    refreshUser,
    setUser,
    clearError,
    checkAuth
  }
})
