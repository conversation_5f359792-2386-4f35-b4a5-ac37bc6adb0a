<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center p-4">
    <div class="card max-w-md w-full text-center">
      <div v-if="isLoading" class="py-8">
        <div class="w-16 h-16 border-4 border-line-green border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">處理登入中...</h2>
        <p class="text-gray-600">{{ processingStep }}</p>

        <!-- 進度指示 -->
        <div class="mt-6 max-w-xs mx-auto">
          <div class="bg-gray-200 rounded-full h-2">
            <div class="bg-line-green h-2 rounded-full animate-pulse" style="width: 60%"></div>
          </div>
        </div>
      </div>
      
      <div v-else-if="error" class="py-8">
        <div class="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
          <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
        </div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">登入失敗</h2>
        <p class="text-gray-600 mb-6">{{ error }}</p>
        <button
          @click="router.push('/login')"
          class="btn-primary"
        >
          重新登入
        </button>
      </div>
      
      <div v-else class="py-8">
        <div class="w-16 h-16 bg-green-100 rounded-full mx-auto mb-4 flex items-center justify-center">
          <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
        </div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">登入成功</h2>
        <p class="text-gray-600 mb-6">正在跳轉到首頁...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const isLoading = ref(true)
const error = ref<string | null>(null)
const processingStep = ref('驗證登入資訊...')

onMounted(async () => {
  try {
    // 清除之前的錯誤
    authStore.clearError()

    // 檢查 URL 中的錯誤參數
    if (route.query.error) {
      throw new Error(route.query.error_description as string || '登入失敗')
    }

    // 處理不同類型的回調
    if (route.query.code && route.query.state) {
      // 直接 LINE Login 回調
      await handleDirectLineCallback()
    } else if (route.hash.includes('access_token') || route.query.access_token) {
      // Supabase Auth 回調
      await handleSupabaseCallback()
    } else {
      // 檢查是否已經有會話（可能是從其他頁面重定向過來的）
      await checkExistingSession()
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '未知錯誤'
    console.error('認證回調處理失敗:', err)
  } finally {
    isLoading.value = false
  }
})

// 處理直接 LINE Login 回調
const handleDirectLineCallback = async () => {
  processingStep.value = '處理 LINE 登入...'

  const code = route.query.code as string
  const state = route.query.state as string

  if (!code || !state) {
    throw new Error('缺少必要的認證參數')
  }

  // 使用 auth store 處理回調
  await authStore.handleLineCallback(code, state)

  processingStep.value = '登入成功，正在跳轉...'

  // 等待一下讓用戶看到成功訊息
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 跳轉到目標頁面
  const redirectTo = (route.query.redirect as string) || '/'
  router.replace(redirectTo)
}

// 處理 Supabase Auth 回調
const handleSupabaseCallback = async () => {
  processingStep.value = '處理 Supabase 認證...'

  // Supabase 會自動處理 hash 中的 token
  // 我們只需要等待認證狀態更新
  await new Promise(resolve => setTimeout(resolve, 2000))

  // 檢查是否成功登入
  if (authStore.isAuthenticated) {
    processingStep.value = '登入成功，正在跳轉...'

    await new Promise(resolve => setTimeout(resolve, 1000))

    const redirectTo = (route.query.redirect as string) || '/'
    router.replace(redirectTo)
  } else {
    throw new Error('Supabase 認證失敗')
  }
}

// 檢查現有會話
const checkExistingSession = async () => {
  processingStep.value = '檢查登入狀態...'

  // 初始化認證狀態
  await authStore.initAuth()

  if (authStore.isAuthenticated) {
    processingStep.value = '已登入，正在跳轉...'

    await new Promise(resolve => setTimeout(resolve, 1000))

    const redirectTo = (route.query.redirect as string) || '/'
    router.replace(redirectTo)
  } else {
    throw new Error('未找到有效的登入會話')
  }
}
</script>
