<template>
  <div class="min-h-screen bg-gradient-to-br from-line-green to-green-600 flex items-center justify-center p-4">
    <div class="card max-w-md w-full text-center">
      <!-- Logo 區域 -->
      <div class="mb-8">
        <div class="w-20 h-20 bg-line-green rounded-full mx-auto mb-4 flex items-center justify-center">
          <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">線上預約系統</h1>
        <p class="text-gray-600">使用 LINE 帳號快速登入</p>
      </div>

      <!-- 登入按鈕 -->
      <div class="space-y-4">
        <!-- 主要 LINE 登入按鈕 -->
        <button
          @click="handleLineLogin"
          :disabled="isLoading"
          class="w-full bg-line-green hover:bg-green-600 text-white font-medium py-4 px-6 rounded-xl transition-colors duration-200 shadow-soft flex items-center justify-center space-x-3 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg v-if="!isLoading" class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19.365 9.863c.349 0 .63.285.63.631 0 .345-.281.63-.63.63H17.61v1.125h1.755c.349 0 .63.283.63.63 0 .344-.281.629-.63.629h-2.386c-.345 0-.627-.285-.627-.629V8.108c0-.345.282-.63.627-.63h2.386c.349 0 .63.285.63.63 0 .349-.281.63-.63.63H17.61v1.125h1.755zm-3.855 3.016c0 .27-.174.51-.432.596-.064.021-.133.031-.199.031-.211 0-.391-.09-.51-.25l-2.443-3.317v2.94c0 .344-.279.629-.631.629-.346 0-.626-.285-.626-.629V8.108c0-.27.173-.51.43-.595.06-.023.136-.033.194-.033.195 0 .375.104.495.254l2.462 3.33V8.108c0-.345.282-.63.63-.63.345 0 .63.285.63.63v4.771zm-5.741 0c0 .344-.282.629-.631.629-.345 0-.627-.285-.627-.629V8.108c0-.345.282-.63.627-.63.349 0 .631.285.631.63v4.771zm-2.466.629H4.917c-.345 0-.63-.285-.63-.629V8.108c0-.345.285-.63.63-.63.348 0 .63.285.63.63v4.141h1.756c.348 0 .629.283.629.63 0 .344-.281.629-.629.629M24 10.314C24 4.943 18.615.572 12 .572S0 4.943 0 10.314c0 4.811 4.27 8.842 10.035 9.608.391.082.923.258 1.058.59.12.301.079.766.038 1.08l-.164 1.02c-.045.301-.24 1.186 1.049.645 1.291-.539 6.916-4.078 9.436-6.975C23.176 14.393 24 12.458 24 10.314"/>
          </svg>
          <div v-else class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span>{{ isLoading ? '登入中...' : '使用 LINE 登入' }}</span>
        </button>

        <!-- 備用登入方式 -->
        <div v-if="showAlternativeLogin" class="space-y-2">
          <div class="text-center text-sm text-gray-500">或</div>
          <button
            @click="handleDirectLineLogin"
            :disabled="isLoading"
            class="w-full bg-white hover:bg-gray-50 text-gray-700 font-medium py-3 px-6 rounded-xl border border-gray-200 transition-colors duration-200 shadow-soft flex items-center justify-center space-x-3 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg class="w-5 h-5 text-line-green" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19.365 9.863c.349 0 .63.285.63.631 0 .345-.281.63-.63.63H17.61v1.125h1.755c.349 0 .63.283.63.63 0 .344-.281.629-.63.629h-2.386c-.345 0-.627-.285-.627-.629V8.108c0-.345.282-.63.627-.63h2.386c.349 0 .63.285.63.63 0 .349-.281.63-.63.63H17.61v1.125h1.755zm-3.855 3.016c0 .27-.174.51-.432.596-.064.021-.133.031-.199.031-.211 0-.391-.09-.51-.25l-2.443-3.317v2.94c0 .344-.279.629-.631.629-.346 0-.626-.285-.626-.629V8.108c0-.27.173-.51.43-.595.06-.023.136-.033.194-.033.195 0 .375.104.495.254l2.462 3.33V8.108c0-.345.282-.63.63-.63.345 0 .63.285.63.63v4.771zm-5.741 0c0 .344-.282.629-.631.629-.345 0-.627-.285-.627-.629V8.108c0-.345.282-.63.627-.63.349 0 .631.285.631.63v4.771zm-2.466.629H4.917c-.345 0-.63-.285-.63-.629V8.108c0-.345.285-.63.63-.63.348 0 .63.285.63.63v4.141h1.756c.348 0 .629.283.629.63 0 .344-.281.629-.629.629M24 10.314C24 4.943 18.615.572 12 .572S0 4.943 0 10.314c0 4.811 4.27 8.842 10.035 9.608.391.082.923.258 1.058.59.12.301.079.766.038 1.08l-.164 1.02c-.045.301-.24 1.186 1.049.645 1.291-.539 6.916-4.078 9.436-6.975C23.176 14.393 24 12.458 24 10.314"/>
            </svg>
            <span>直接 LINE 登入</span>
          </button>
        </div>

        <!-- 錯誤訊息 -->
        <div v-if="authStore.error" class="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            <span class="text-sm text-red-700">{{ authStore.error }}</span>
          </div>
          <button
            @click="authStore.clearError"
            class="mt-2 text-xs text-red-600 hover:text-red-800 underline"
          >
            關閉
          </button>
        </div>

        <!-- 配置檢查 -->
        <div v-if="!isLineConfigured" class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            <span class="text-sm text-yellow-700">LINE Login 尚未配置，請檢查環境變數</span>
          </div>
        </div>

        <div class="text-sm text-gray-500">
          <p>登入即表示您同意我們的</p>
          <div class="flex justify-center space-x-4 mt-1">
            <a href="#" class="text-line-green hover:underline">服務條款</a>
            <span>|</span>
            <a href="#" class="text-line-green hover:underline">隱私政策</a>
          </div>
        </div>
      </div>

      <!-- 功能介紹 -->
      <div class="mt-8 pt-8 border-t border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">服務特色</h3>
        <div class="grid grid-cols-1 gap-4 text-sm text-gray-600">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
            </div>
            <span>快速預約，無需等待</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
              </svg>
            </div>
            <span>彈性時間安排</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
              </svg>
            </div>
            <span>票券優惠折抵</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { lineAuth } from '@/services/lineAuth'

const router = useRouter()
const authStore = useAuthStore()
const isLoading = computed(() => authStore.isLoading)
const showAlternativeLogin = ref(false)
const isLineConfigured = ref(true)

// 檢查 LINE Login 配置
onMounted(() => {
  isLineConfigured.value = lineAuth.isConfigured()
  if (!isLineConfigured.value) {
    console.warn('LINE Login 未配置，請檢查環境變數')
  }
})

// 主要 LINE 登入方法（使用 Supabase Auth）
const handleLineLogin = async () => {
  try {
    authStore.clearError()
    await authStore.signInWithLine()
    // 登入成功後會透過 Supabase Auth 自動重定向
  } catch (error) {
    console.error('LINE 登入失敗:', error)
    // 如果 Supabase Auth 失敗，顯示備用選項
    showAlternativeLogin.value = true
  }
}

// 直接 LINE 登入方法（不使用 Supabase Auth）
const handleDirectLineLogin = async () => {
  try {
    authStore.clearError()
    await authStore.signInWithLineDirect()
    // 這會直接重定向到 LINE，不需要等待
  } catch (error) {
    console.error('直接 LINE 登入失敗:', error)
  }
}

// 檢查是否已登入
onMounted(async () => {
  if (authStore.isAuthenticated) {
    router.replace('/')
  }
})
</script>
