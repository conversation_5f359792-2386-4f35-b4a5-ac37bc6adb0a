<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 頂部導航 -->
    <header class="bg-white shadow-sm">
      <div class="max-w-md mx-auto px-4 py-4 flex items-center justify-between">
        <button
          @click="router.push('/')"
          class="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
        </button>
        
        <h1 class="text-lg font-semibold text-gray-900">會員中心</h1>
        
        <button
          @click="handleLogout"
          class="text-sm text-gray-600 hover:text-gray-900"
        >
          登出
        </button>
      </div>
    </header>

    <!-- 用戶資訊卡片 -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-md mx-auto px-4 py-6">
        <div class="flex items-center space-x-4">
          <!-- 頭像 -->
          <div class="relative">
            <img
              v-if="authStore.user?.picture_url"
              :src="authStore.user.picture_url"
              :alt="authStore.user.display_name"
              class="w-16 h-16 rounded-full object-cover"
            />
            <div
              v-else
              class="w-16 h-16 bg-gradient-to-br from-line-green to-green-600 rounded-full flex items-center justify-center"
            >
              <span class="text-white font-semibold text-xl">
                {{ authStore.user?.display_name?.charAt(0) || 'U' }}
              </span>
            </div>
          </div>

          <!-- 用戶資訊 -->
          <div class="flex-1 min-w-0">
            <h2 class="text-lg font-semibold text-gray-900 truncate">
              {{ authStore.user?.display_name || '用戶' }}
            </h2>
            <p v-if="authStore.user?.email" class="text-sm text-gray-600 truncate">
              {{ authStore.user.email }}
            </p>
            <p class="text-xs text-gray-500 mt-1">
              會員編號：{{ authStore.user?.id?.slice(-8) }}
            </p>
          </div>

          <!-- 編輯按鈕 -->
          <button
            @click="router.push('/member')"
            class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 導航標籤 -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-md mx-auto">
        <nav class="flex">
          <router-link
            to="/member"
            class="flex-1 py-4 px-4 text-center text-sm font-medium transition-colors duration-200"
            :class="route.name === 'member-profile' 
              ? 'text-line-green border-b-2 border-line-green' 
              : 'text-gray-500 hover:text-gray-700'"
          >
            個人資料
          </router-link>
          <router-link
            to="/member/bookings"
            class="flex-1 py-4 px-4 text-center text-sm font-medium transition-colors duration-200"
            :class="route.name === 'member-bookings' 
              ? 'text-line-green border-b-2 border-line-green' 
              : 'text-gray-500 hover:text-gray-700'"
          >
            預約記錄
          </router-link>
          <router-link
            to="/member/tickets"
            class="flex-1 py-4 px-4 text-center text-sm font-medium transition-colors duration-200"
            :class="route.name === 'member-tickets' 
              ? 'text-line-green border-b-2 border-line-green' 
              : 'text-gray-500 hover:text-gray-700'"
          >
            我的票券
          </router-link>
        </nav>
      </div>
    </div>

    <!-- 主要內容 -->
    <main class="max-w-md mx-auto px-4 py-6">
      <router-view />
    </main>

    <!-- 底部快速操作 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <div class="max-w-md mx-auto">
        <button
          @click="router.push('/booking')"
          class="btn-primary w-full"
        >
          立即預約
        </button>
      </div>
    </div>

    <!-- 底部間距 -->
    <div class="h-20"></div>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const handleLogout = async () => {
  try {
    await authStore.signOut()
    router.push('/')
  } catch (error) {
    console.error('登出失敗:', error)
  }
}
</script>
