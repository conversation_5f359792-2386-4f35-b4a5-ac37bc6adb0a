<template>
  <div class="space-y-6">
    <!-- 個人資料編輯 -->
    <div class="card">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">個人資料</h3>
      
      <form @submit.prevent="updateProfile" class="space-y-4">
        <!-- 顯示名稱 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            顯示名稱
          </label>
          <input
            v-model="profileForm.display_name"
            type="text"
            class="input-field"
            placeholder="請輸入您的顯示名稱"
          />
        </div>

        <!-- 電子郵件 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            電子郵件
          </label>
          <input
            v-model="profileForm.email"
            type="email"
            class="input-field"
            placeholder="請輸入您的電子郵件"
          />
        </div>

        <!-- 手機號碼 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            手機號碼
          </label>
          <input
            v-model="profileForm.phone"
            type="tel"
            class="input-field"
            placeholder="請輸入您的手機號碼"
          />
        </div>

        <!-- 儲存按鈕 -->
        <button
          type="submit"
          :disabled="isUpdating"
          class="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <div v-if="isUpdating" class="flex items-center justify-center space-x-2">
            <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span>更新中...</span>
          </div>
          <span v-else>儲存變更</span>
        </button>
      </form>
    </div>

    <!-- 帳號資訊 -->
    <div class="card">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">帳號資訊</h3>
      
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-gray-600">會員編號</span>
          <span class="font-medium text-gray-900">{{ authStore.user?.id?.slice(-8) }}</span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-gray-600">LINE 用戶 ID</span>
          <span class="font-medium text-gray-900">{{ authStore.user?.line_user_id?.slice(-8) }}</span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-gray-600">註冊時間</span>
          <span class="font-medium text-gray-900">{{ formatDate(authStore.user?.created_at) }}</span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-gray-600">最後更新</span>
          <span class="font-medium text-gray-900">{{ formatDate(authStore.user?.updated_at) }}</span>
        </div>
      </div>
    </div>

    <!-- 統計資訊 -->
    <div class="card">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">使用統計</h3>
      
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center p-4 bg-blue-50 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 mb-1">{{ stats.totalBookings }}</div>
          <div class="text-sm text-gray-600">總預約次數</div>
        </div>
        
        <div class="text-center p-4 bg-green-50 rounded-lg">
          <div class="text-2xl font-bold text-green-600 mb-1">{{ stats.completedBookings }}</div>
          <div class="text-sm text-gray-600">完成預約</div>
        </div>
        
        <div class="text-center p-4 bg-purple-50 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 mb-1">{{ stats.availableTickets }}</div>
          <div class="text-sm text-gray-600">可用票券</div>
        </div>
        
        <div class="text-center p-4 bg-orange-50 rounded-lg">
          <div class="text-2xl font-bold text-orange-600 mb-1">{{ stats.usedTickets }}</div>
          <div class="text-sm text-gray-600">已用票券</div>
        </div>
      </div>
    </div>

    <!-- 偏好設定 -->
    <div class="card">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">偏好設定</h3>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="font-medium text-gray-900">預約提醒</p>
            <p class="text-sm text-gray-600">在預約前 1 小時發送 LINE 提醒</p>
          </div>
          <button
            @click="toggleNotification('booking_reminder')"
            class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200"
            :class="preferences.booking_reminder ? 'bg-line-green' : 'bg-gray-200'"
          >
            <span
              class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200"
              :class="preferences.booking_reminder ? 'translate-x-6' : 'translate-x-1'"
            ></span>
          </button>
        </div>
        
        <div class="flex items-center justify-between">
          <div>
            <p class="font-medium text-gray-900">優惠通知</p>
            <p class="text-sm text-gray-600">接收新票券和優惠活動通知</p>
          </div>
          <button
            @click="toggleNotification('promotion_notification')"
            class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200"
            :class="preferences.promotion_notification ? 'bg-line-green' : 'bg-gray-200'"
          >
            <span
              class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200"
              :class="preferences.promotion_notification ? 'translate-x-6' : 'translate-x-1'"
            ></span>
          </button>
        </div>
      </div>
    </div>

    <!-- 危險操作 -->
    <div class="card border border-red-200">
      <h3 class="text-lg font-semibold text-red-600 mb-4">危險操作</h3>
      
      <div class="space-y-3">
        <button
          @click="showDeleteConfirm = true"
          class="w-full py-3 px-4 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200"
        >
          刪除帳號
        </button>
        
        <p class="text-xs text-gray-500">
          刪除帳號將永久移除您的所有資料，此操作無法復原
        </p>
      </div>
    </div>

    <!-- 刪除確認對話框 -->
    <div
      v-if="showDeleteConfirm"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      @click="showDeleteConfirm = false"
    >
      <div
        class="bg-white rounded-lg p-6 max-w-sm w-full"
        @click.stop
      >
        <h4 class="text-lg font-semibold text-gray-900 mb-2">確認刪除帳號</h4>
        <p class="text-gray-600 mb-4">
          此操作將永久刪除您的帳號和所有相關資料，包括預約記錄和票券。此操作無法復原。
        </p>
        
        <div class="flex space-x-3">
          <button
            @click="showDeleteConfirm = false"
            class="flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="deleteAccount"
            class="flex-1 py-2 px-4 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            確認刪除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 表單資料
const profileForm = reactive({
  display_name: '',
  email: '',
  phone: ''
})

// 狀態
const isUpdating = ref(false)
const showDeleteConfirm = ref(false)

// 統計資料
const stats = reactive({
  totalBookings: 0,
  completedBookings: 0,
  availableTickets: 0,
  usedTickets: 0
})

// 偏好設定
const preferences = reactive({
  booking_reminder: true,
  promotion_notification: true
})

// 方法
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-TW')
}

const updateProfile = async () => {
  isUpdating.value = true
  try {
    // 這裡實現更新個人資料的邏輯
    console.log('更新個人資料:', profileForm)
    
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新成功提示
    alert('個人資料更新成功！')
  } catch (error) {
    console.error('更新失敗:', error)
    alert('更新失敗，請稍後再試')
  } finally {
    isUpdating.value = false
  }
}

const toggleNotification = (type: string) => {
  preferences[type as keyof typeof preferences] = !preferences[type as keyof typeof preferences]
  // 這裡可以實現保存偏好設定的邏輯
  console.log('偏好設定更新:', preferences)
}

const deleteAccount = async () => {
  try {
    // 這裡實現刪除帳號的邏輯
    console.log('刪除帳號')
    
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 登出並跳轉
    await authStore.signOut()
    window.location.href = '/'
  } catch (error) {
    console.error('刪除帳號失敗:', error)
    alert('刪除失敗，請稍後再試')
  }
  showDeleteConfirm.value = false
}

// 載入資料
const loadUserStats = async () => {
  try {
    // 這裡實現載入用戶統計資料的邏輯
    // 模擬資料
    stats.totalBookings = 12
    stats.completedBookings = 10
    stats.availableTickets = 3
    stats.usedTickets = 5
  } catch (error) {
    console.error('載入統計資料失敗:', error)
  }
}

// 初始化
onMounted(() => {
  // 填入現有的用戶資料
  if (authStore.user) {
    profileForm.display_name = authStore.user.display_name || ''
    profileForm.email = authStore.user.email || ''
    profileForm.phone = authStore.user.phone || ''
  }
  
  // 載入統計資料
  loadUserStats()
})
</script>
